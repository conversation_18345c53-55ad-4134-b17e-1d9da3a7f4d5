[{"name":"hot-reloader","duration":101,"timestamp":3307476075666,"id":3,"tags":{"version":"15.3.4"},"startTime":1750786480575,"traceId":"027b520ae38c0c54"},{"name":"start","duration":2,"timestamp":3307476076238,"id":4,"parentId":3,"tags":{},"startTime":1750786480576,"traceId":"027b520ae38c0c54"},{"name":"get-version-info","duration":813530,"timestamp":3307476076255,"id":5,"parentId":4,"tags":{},"startTime":1750786480576,"traceId":"027b520ae38c0c54"},{"name":"clean","duration":6250,"timestamp":3307476889943,"id":6,"parentId":4,"tags":{},"startTime":1750786481389,"traceId":"027b520ae38c0c54"},{"name":"create-pages-mapping","duration":186,"timestamp":3307476897188,"id":8,"parentId":7,"tags":{},"startTime":1750786481397,"traceId":"027b520ae38c0c54"},{"name":"create-entrypoints","duration":12733,"timestamp":3307476897393,"id":9,"parentId":7,"tags":{},"startTime":1750786481397,"traceId":"027b520ae38c0c54"},{"name":"generate-webpack-config","duration":87419,"timestamp":3307476910161,"id":10,"parentId":7,"tags":{},"startTime":1750786481410,"traceId":"027b520ae38c0c54"},{"name":"get-webpack-config","duration":100495,"timestamp":3307476897099,"id":7,"parentId":4,"tags":{},"startTime":1750786481397,"traceId":"027b520ae38c0c54"},{"name":"make","duration":582,"timestamp":3307477041414,"id":12,"parentId":11,"tags":{},"startTime":1750786481541,"traceId":"027b520ae38c0c54"},{"name":"chunk-graph","duration":356,"timestamp":3307477043070,"id":14,"parentId":13,"tags":{},"startTime":1750786481543,"traceId":"027b520ae38c0c54"},{"name":"optimize-modules","duration":10,"timestamp":3307477043480,"id":16,"parentId":13,"tags":{},"startTime":1750786481543,"traceId":"027b520ae38c0c54"},{"name":"optimize-chunks","duration":66,"timestamp":3307477043589,"id":17,"parentId":13,"tags":{},"startTime":1750786481543,"traceId":"027b520ae38c0c54"},{"name":"optimize-tree","duration":11,"timestamp":3307477043681,"id":18,"parentId":13,"tags":{},"startTime":1750786481543,"traceId":"027b520ae38c0c54"},{"name":"optimize-chunk-modules","duration":12,"timestamp":3307477043781,"id":19,"parentId":13,"tags":{},"startTime":1750786481543,"traceId":"027b520ae38c0c54"},{"name":"optimize","duration":379,"timestamp":3307477043459,"id":15,"parentId":13,"tags":{},"startTime":1750786481543,"traceId":"027b520ae38c0c54"},{"name":"module-hash","duration":51,"timestamp":3307477044161,"id":20,"parentId":13,"tags":{},"startTime":1750786481544,"traceId":"027b520ae38c0c54"},{"name":"code-generation","duration":77,"timestamp":3307477044223,"id":21,"parentId":13,"tags":{},"startTime":1750786481544,"traceId":"027b520ae38c0c54"},{"name":"hash","duration":236,"timestamp":3307477044430,"id":22,"parentId":13,"tags":{},"startTime":1750786481544,"traceId":"027b520ae38c0c54"},{"name":"code-generation-jobs","duration":29,"timestamp":3307477044665,"id":23,"parentId":13,"tags":{},"startTime":1750786481544,"traceId":"027b520ae38c0c54"},{"name":"module-assets","duration":37,"timestamp":3307477044681,"id":24,"parentId":13,"tags":{},"startTime":1750786481544,"traceId":"027b520ae38c0c54"},{"name":"create-chunk-assets","duration":134,"timestamp":3307477044723,"id":25,"parentId":13,"tags":{},"startTime":1750786481544,"traceId":"027b520ae38c0c54"},{"name":"NextJsBuildManifest-generateClientManifest","duration":643,"timestamp":3307477066079,"id":27,"parentId":11,"tags":{},"startTime":1750786481566,"traceId":"027b520ae38c0c54"},{"name":"NextJsBuildManifest-createassets","duration":919,"timestamp":3307477065815,"id":26,"parentId":11,"tags":{},"startTime":1750786481565,"traceId":"027b520ae38c0c54"},{"name":"seal","duration":24425,"timestamp":3307477042981,"id":13,"parentId":11,"tags":{},"startTime":1750786481543,"traceId":"027b520ae38c0c54"},{"name":"webpack-compilation","duration":28055,"timestamp":3307477039481,"id":11,"parentId":3,"tags":{"name":"client"},"startTime":1750786481539,"traceId":"027b520ae38c0c54"},{"name":"emit","duration":2877,"timestamp":3307477067729,"id":28,"parentId":3,"tags":{},"startTime":1750786481567,"traceId":"027b520ae38c0c54"},{"name":"make","duration":710,"timestamp":3307477074977,"id":30,"parentId":29,"tags":{},"startTime":1750786481575,"traceId":"027b520ae38c0c54"},{"name":"chunk-graph","duration":15,"timestamp":3307477075813,"id":32,"parentId":31,"tags":{},"startTime":1750786481575,"traceId":"027b520ae38c0c54"},{"name":"optimize-modules","duration":2,"timestamp":3307477075839,"id":34,"parentId":31,"tags":{},"startTime":1750786481575,"traceId":"027b520ae38c0c54"},{"name":"optimize-chunks","duration":280,"timestamp":3307477075872,"id":35,"parentId":31,"tags":{},"startTime":1750786481575,"traceId":"027b520ae38c0c54"},{"name":"optimize-tree","duration":4,"timestamp":3307477076171,"id":36,"parentId":31,"tags":{},"startTime":1750786481576,"traceId":"027b520ae38c0c54"},{"name":"optimize-chunk-modules","duration":4,"timestamp":3307477076204,"id":37,"parentId":31,"tags":{},"startTime":1750786481576,"traceId":"027b520ae38c0c54"},{"name":"optimize","duration":401,"timestamp":3307477075836,"id":33,"parentId":31,"tags":{},"startTime":1750786481575,"traceId":"027b520ae38c0c54"},{"name":"module-hash","duration":5,"timestamp":3307477076357,"id":38,"parentId":31,"tags":{},"startTime":1750786481576,"traceId":"027b520ae38c0c54"},{"name":"code-generation","duration":4,"timestamp":3307477076368,"id":39,"parentId":31,"tags":{},"startTime":1750786481576,"traceId":"027b520ae38c0c54"},{"name":"hash","duration":29,"timestamp":3307477076391,"id":40,"parentId":31,"tags":{},"startTime":1750786481576,"traceId":"027b520ae38c0c54"},{"name":"code-generation-jobs","duration":20,"timestamp":3307477076421,"id":41,"parentId":31,"tags":{},"startTime":1750786481576,"traceId":"027b520ae38c0c54"},{"name":"module-assets","duration":6,"timestamp":3307477076437,"id":42,"parentId":31,"tags":{},"startTime":1750786481576,"traceId":"027b520ae38c0c54"},{"name":"create-chunk-assets","duration":7,"timestamp":3307477076446,"id":43,"parentId":31,"tags":{},"startTime":1750786481576,"traceId":"027b520ae38c0c54"},{"name":"seal","duration":1234,"timestamp":3307477075794,"id":31,"parentId":29,"tags":{},"startTime":1750786481575,"traceId":"027b520ae38c0c54"},{"name":"webpack-compilation","duration":2730,"timestamp":3307477074370,"id":29,"parentId":3,"tags":{"name":"server"},"startTime":1750786481574,"traceId":"027b520ae38c0c54"},{"name":"emit","duration":796,"timestamp":3307477077131,"id":44,"parentId":3,"tags":{},"startTime":1750786481577,"traceId":"027b520ae38c0c54"},{"name":"make","duration":87,"timestamp":3307477080344,"id":46,"parentId":45,"tags":{},"startTime":1750786481580,"traceId":"027b520ae38c0c54"},{"name":"chunk-graph","duration":15,"timestamp":3307477080712,"id":48,"parentId":47,"tags":{},"startTime":1750786481580,"traceId":"027b520ae38c0c54"},{"name":"optimize-modules","duration":2,"timestamp":3307477080738,"id":50,"parentId":47,"tags":{},"startTime":1750786481580,"traceId":"027b520ae38c0c54"},{"name":"optimize-chunks","duration":6,"timestamp":3307477080769,"id":51,"parentId":47,"tags":{},"startTime":1750786481580,"traceId":"027b520ae38c0c54"},{"name":"optimize-tree","duration":7,"timestamp":3307477080783,"id":52,"parentId":47,"tags":{},"startTime":1750786481580,"traceId":"027b520ae38c0c54"},{"name":"optimize-chunk-modules","duration":3,"timestamp":3307477080801,"id":53,"parentId":47,"tags":{},"startTime":1750786481580,"traceId":"027b520ae38c0c54"},{"name":"optimize","duration":83,"timestamp":3307477080734,"id":49,"parentId":47,"tags":{},"startTime":1750786481580,"traceId":"027b520ae38c0c54"},{"name":"module-hash","duration":4,"timestamp":3307477080871,"id":54,"parentId":47,"tags":{},"startTime":1750786481580,"traceId":"027b520ae38c0c54"},{"name":"code-generation","duration":4,"timestamp":3307477080881,"id":55,"parentId":47,"tags":{},"startTime":1750786481580,"traceId":"027b520ae38c0c54"},{"name":"hash","duration":39,"timestamp":3307477080907,"id":56,"parentId":47,"tags":{},"startTime":1750786481580,"traceId":"027b520ae38c0c54"},{"name":"code-generation-jobs","duration":10,"timestamp":3307477080946,"id":57,"parentId":47,"tags":{},"startTime":1750786481580,"traceId":"027b520ae38c0c54"},{"name":"module-assets","duration":4,"timestamp":3307477080953,"id":58,"parentId":47,"tags":{},"startTime":1750786481580,"traceId":"027b520ae38c0c54"},{"name":"create-chunk-assets","duration":6,"timestamp":3307477080960,"id":59,"parentId":47,"tags":{},"startTime":1750786481580,"traceId":"027b520ae38c0c54"},{"name":"seal","duration":651,"timestamp":3307477080689,"id":47,"parentId":45,"tags":{},"startTime":1750786481580,"traceId":"027b520ae38c0c54"},{"name":"webpack-compilation","duration":1814,"timestamp":3307477079545,"id":45,"parentId":3,"tags":{"name":"edge-server"},"startTime":1750786481579,"traceId":"027b520ae38c0c54"},{"name":"emit","duration":726,"timestamp":3307477081379,"id":60,"parentId":3,"tags":{},"startTime":1750786481581,"traceId":"027b520ae38c0c54"}]
[{"name":"make","duration":274,"timestamp":3307477295199,"id":65,"parentId":64,"tags":{},"startTime":1750786481795,"traceId":"027b520ae38c0c54"},{"name":"chunk-graph","duration":15,"timestamp":3307477295579,"id":67,"parentId":66,"tags":{},"startTime":1750786481795,"traceId":"027b520ae38c0c54"},{"name":"optimize-modules","duration":3,"timestamp":3307477295605,"id":69,"parentId":66,"tags":{},"startTime":1750786481795,"traceId":"027b520ae38c0c54"},{"name":"optimize-chunks","duration":6,"timestamp":3307477295624,"id":70,"parentId":66,"tags":{},"startTime":1750786481795,"traceId":"027b520ae38c0c54"},{"name":"optimize-tree","duration":3,"timestamp":3307477295637,"id":71,"parentId":66,"tags":{},"startTime":1750786481795,"traceId":"027b520ae38c0c54"},{"name":"optimize-chunk-modules","duration":3,"timestamp":3307477295652,"id":72,"parentId":66,"tags":{},"startTime":1750786481795,"traceId":"027b520ae38c0c54"},{"name":"optimize","duration":68,"timestamp":3307477295601,"id":68,"parentId":66,"tags":{},"startTime":1750786481795,"traceId":"027b520ae38c0c54"},{"name":"module-hash","duration":5,"timestamp":3307477295726,"id":73,"parentId":66,"tags":{},"startTime":1750786481795,"traceId":"027b520ae38c0c54"},{"name":"code-generation","duration":4,"timestamp":3307477295735,"id":74,"parentId":66,"tags":{},"startTime":1750786481795,"traceId":"027b520ae38c0c54"},{"name":"hash","duration":29,"timestamp":3307477295757,"id":75,"parentId":66,"tags":{},"startTime":1750786481795,"traceId":"027b520ae38c0c54"},{"name":"code-generation-jobs","duration":10,"timestamp":3307477295786,"id":76,"parentId":66,"tags":{},"startTime":1750786481795,"traceId":"027b520ae38c0c54"},{"name":"module-assets","duration":4,"timestamp":3307477295794,"id":77,"parentId":66,"tags":{},"startTime":1750786481795,"traceId":"027b520ae38c0c54"},{"name":"create-chunk-assets","duration":7,"timestamp":3307477295801,"id":78,"parentId":66,"tags":{},"startTime":1750786481795,"traceId":"027b520ae38c0c54"},{"name":"NextJsBuildManifest-generateClientManifest","duration":247,"timestamp":3307477296061,"id":80,"parentId":64,"tags":{},"startTime":1750786481796,"traceId":"027b520ae38c0c54"},{"name":"NextJsBuildManifest-createassets","duration":340,"timestamp":3307477295971,"id":79,"parentId":64,"tags":{},"startTime":1750786481795,"traceId":"027b520ae38c0c54"},{"name":"seal","duration":836,"timestamp":3307477295559,"id":66,"parentId":64,"tags":{},"startTime":1750786481795,"traceId":"027b520ae38c0c54"},{"name":"webpack-compilation","duration":1615,"timestamp":3307477294795,"id":64,"parentId":61,"tags":{"name":"client"},"startTime":1750786481794,"traceId":"027b520ae38c0c54"},{"name":"emit","duration":1014,"timestamp":3307477296423,"id":81,"parentId":61,"tags":{},"startTime":1750786481796,"traceId":"027b520ae38c0c54"},{"name":"webpack-invalidated-client","duration":5118,"timestamp":3307477292833,"id":61,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750786481792,"traceId":"027b520ae38c0c54"},{"name":"make","duration":156,"timestamp":3307477298938,"id":83,"parentId":82,"tags":{},"startTime":1750786481798,"traceId":"027b520ae38c0c54"},{"name":"chunk-graph","duration":17,"timestamp":3307477299167,"id":85,"parentId":84,"tags":{},"startTime":1750786481799,"traceId":"027b520ae38c0c54"},{"name":"optimize-modules","duration":2,"timestamp":3307477299193,"id":87,"parentId":84,"tags":{},"startTime":1750786481799,"traceId":"027b520ae38c0c54"},{"name":"optimize-chunks","duration":21,"timestamp":3307477299230,"id":88,"parentId":84,"tags":{},"startTime":1750786481799,"traceId":"027b520ae38c0c54"},{"name":"optimize-tree","duration":3,"timestamp":3307477299257,"id":89,"parentId":84,"tags":{},"startTime":1750786481799,"traceId":"027b520ae38c0c54"},{"name":"optimize-chunk-modules","duration":2,"timestamp":3307477299269,"id":90,"parentId":84,"tags":{},"startTime":1750786481799,"traceId":"027b520ae38c0c54"},{"name":"optimize","duration":93,"timestamp":3307477299189,"id":86,"parentId":84,"tags":{},"startTime":1750786481799,"traceId":"027b520ae38c0c54"},{"name":"module-hash","duration":4,"timestamp":3307477299452,"id":91,"parentId":84,"tags":{},"startTime":1750786481799,"traceId":"027b520ae38c0c54"},{"name":"code-generation","duration":4,"timestamp":3307477299461,"id":92,"parentId":84,"tags":{},"startTime":1750786481799,"traceId":"027b520ae38c0c54"},{"name":"hash","duration":32,"timestamp":3307477299481,"id":93,"parentId":84,"tags":{},"startTime":1750786481799,"traceId":"027b520ae38c0c54"},{"name":"code-generation-jobs","duration":9,"timestamp":3307477299513,"id":94,"parentId":84,"tags":{},"startTime":1750786481799,"traceId":"027b520ae38c0c54"},{"name":"module-assets","duration":4,"timestamp":3307477299520,"id":95,"parentId":84,"tags":{},"startTime":1750786481799,"traceId":"027b520ae38c0c54"},{"name":"create-chunk-assets","duration":6,"timestamp":3307477299527,"id":96,"parentId":84,"tags":{},"startTime":1750786481799,"traceId":"027b520ae38c0c54"},{"name":"seal","duration":600,"timestamp":3307477299152,"id":84,"parentId":82,"tags":{},"startTime":1750786481799,"traceId":"027b520ae38c0c54"},{"name":"webpack-compilation","duration":1183,"timestamp":3307477298584,"id":82,"parentId":62,"tags":{"name":"server"},"startTime":1750786481798,"traceId":"027b520ae38c0c54"},{"name":"setup-dev-bundler","duration":1409039,"timestamp":3307475915741,"id":2,"parentId":1,"tags":{},"startTime":1750786480415,"traceId":"027b520ae38c0c54"},{"name":"emit","duration":26401,"timestamp":3307477299776,"id":97,"parentId":62,"tags":{},"startTime":1750786481799,"traceId":"027b520ae38c0c54"},{"name":"webpack-invalidated-server","duration":33676,"timestamp":3307477292910,"id":62,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750786481792,"traceId":"027b520ae38c0c54"},{"name":"make","duration":131,"timestamp":3307477327845,"id":99,"parentId":98,"tags":{},"startTime":1750786481827,"traceId":"027b520ae38c0c54"},{"name":"chunk-graph","duration":16,"timestamp":3307477328131,"id":101,"parentId":100,"tags":{},"startTime":1750786481828,"traceId":"027b520ae38c0c54"},{"name":"optimize-modules","duration":4,"timestamp":3307477328158,"id":103,"parentId":100,"tags":{},"startTime":1750786481828,"traceId":"027b520ae38c0c54"},{"name":"optimize-chunks","duration":5,"timestamp":3307477328169,"id":104,"parentId":100,"tags":{},"startTime":1750786481828,"traceId":"027b520ae38c0c54"},{"name":"optimize-tree","duration":3,"timestamp":3307477328182,"id":105,"parentId":100,"tags":{},"startTime":1750786481828,"traceId":"027b520ae38c0c54"},{"name":"optimize-chunk-modules","duration":3,"timestamp":3307477328197,"id":106,"parentId":100,"tags":{},"startTime":1750786481828,"traceId":"027b520ae38c0c54"},{"name":"optimize","duration":58,"timestamp":3307477328153,"id":102,"parentId":100,"tags":{},"startTime":1750786481828,"traceId":"027b520ae38c0c54"},{"name":"module-hash","duration":4,"timestamp":3307477328266,"id":107,"parentId":100,"tags":{},"startTime":1750786481828,"traceId":"027b520ae38c0c54"},{"name":"code-generation","duration":5,"timestamp":3307477328277,"id":108,"parentId":100,"tags":{},"startTime":1750786481828,"traceId":"027b520ae38c0c54"},{"name":"hash","duration":98,"timestamp":3307477328300,"id":109,"parentId":100,"tags":{},"startTime":1750786481828,"traceId":"027b520ae38c0c54"},{"name":"code-generation-jobs","duration":11,"timestamp":3307477328398,"id":110,"parentId":100,"tags":{},"startTime":1750786481828,"traceId":"027b520ae38c0c54"},{"name":"module-assets","duration":5,"timestamp":3307477328405,"id":111,"parentId":100,"tags":{},"startTime":1750786481828,"traceId":"027b520ae38c0c54"},{"name":"create-chunk-assets","duration":7,"timestamp":3307477328413,"id":112,"parentId":100,"tags":{},"startTime":1750786481828,"traceId":"027b520ae38c0c54"},{"name":"seal","duration":602,"timestamp":3307477328114,"id":100,"parentId":98,"tags":{},"startTime":1750786481828,"traceId":"027b520ae38c0c54"},{"name":"webpack-compilation","duration":1262,"timestamp":3307477327474,"id":98,"parentId":63,"tags":{"name":"edge-server"},"startTime":1750786481827,"traceId":"027b520ae38c0c54"},{"name":"run-instrumentation-hook","duration":10,"timestamp":3307477349558,"id":114,"parentId":1,"tags":{},"startTime":1750786481849,"traceId":"027b520ae38c0c54"},{"name":"start-dev-server","duration":1702876,"timestamp":3307475649883,"id":1,"tags":{"cpus":"8","platform":"darwin","memory.freeMem":"125321216","memory.totalMem":"8589934592","memory.heapSizeLimit":"4345298944","memory.rss":"215646208","memory.heapTotal":"112689152","memory.heapUsed":"80227816"},"startTime":1750786480149,"traceId":"027b520ae38c0c54"},{"name":"emit","duration":28448,"timestamp":3307477328752,"id":113,"parentId":63,"tags":{},"startTime":1750786481828,"traceId":"027b520ae38c0c54"},{"name":"webpack-invalidated-edge-server","duration":64807,"timestamp":3307477292935,"id":63,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750786481792,"traceId":"027b520ae38c0c54"}]
[{"name":"build-module","duration":27709,"timestamp":3307505666284,"id":122,"parentId":121,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!","layer":"rsc"},"startTime":1750786510166,"traceId":"027b520ae38c0c54"},{"name":"build-module","duration":12628,"timestamp":3307505704786,"id":123,"parentId":122,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/src/app/favicon.ico?__next_metadata__","layer":"rsc"},"startTime":1750786510205,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10473,"timestamp":3307505721350,"id":129,"parentId":122,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/not-found-error.js","layer":"rsc"},"startTime":1750786510221,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11121,"timestamp":3307505721296,"id":128,"parentId":122,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/error-boundary.js","layer":"rsc"},"startTime":1750786510221,"traceId":"027b520ae38c0c54"},{"name":"build-module-tsx","duration":14570,"timestamp":3307505720611,"id":125,"parentId":122,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/src/app/layout.tsx","layer":"rsc"},"startTime":1750786510220,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":15010,"timestamp":3307505721400,"id":130,"parentId":122,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/forbidden-error.js","layer":"rsc"},"startTime":1750786510221,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":15471,"timestamp":3307505721726,"id":131,"parentId":122,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/unauthorized-error.js","layer":"rsc"},"startTime":1750786510222,"traceId":"027b520ae38c0c54"},{"name":"build-module-tsx","duration":24787,"timestamp":3307505718759,"id":124,"parentId":122,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/src/app/page.tsx","layer":"rsc"},"startTime":1750786510219,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":30563,"timestamp":3307505720795,"id":126,"parentId":122,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/route-kind.js","layer":"rsc"},"startTime":1750786510221,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":32448,"timestamp":3307505721086,"id":127,"parentId":122,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/app-render/entry-base.js","layer":"rsc"},"startTime":1750786510221,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":32493,"timestamp":3307505721799,"id":132,"parentId":122,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/route-modules/app-page/module.compiled.js","layer":"ssr"},"startTime":1750786510222,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":268,"timestamp":3307505758524,"id":133,"parentId":132,"tags":{"name":"next/dist/compiled/next-server/app-page.runtime.dev.js","layer":null},"startTime":1750786510258,"traceId":"027b520ae38c0c54"},{"name":"build-module-external","duration":15,"timestamp":3307505762294,"id":138,"parentId":127,"tags":{"name":"../app-render/work-async-storage.external","layer":null},"startTime":1750786510262,"traceId":"027b520ae38c0c54"},{"name":"build-module-external","duration":6,"timestamp":3307505762322,"id":139,"parentId":127,"tags":{"name":"./work-unit-async-storage.external","layer":null},"startTime":1750786510262,"traceId":"027b520ae38c0c54"},{"name":"build-module-external","duration":4,"timestamp":3307505762331,"id":140,"parentId":127,"tags":{"name":"../app-render/action-async-storage.external","layer":null},"startTime":1750786510262,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7752,"timestamp":3307505760907,"id":134,"parentId":129,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/http-access-fallback/error-fallback.js","layer":"rsc"},"startTime":1750786510261,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7031,"timestamp":3307505762252,"id":137,"parentId":127,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/render-from-template-context.js","layer":"rsc"},"startTime":1750786510262,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7208,"timestamp":3307505762383,"id":142,"parentId":127,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/client-segment.js","layer":"rsc"},"startTime":1750786510262,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7402,"timestamp":3307505762339,"id":141,"parentId":127,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/client-page.js","layer":"rsc"},"startTime":1750786510262,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8370,"timestamp":3307505762435,"id":143,"parentId":127,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/hooks-server-context.js","layer":"rsc"},"startTime":1750786510262,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8413,"timestamp":3307505762558,"id":145,"parentId":127,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","layer":"rsc"},"startTime":1750786510262,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9040,"timestamp":3307505762182,"id":136,"parentId":127,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/layout-router.js","layer":"rsc"},"startTime":1750786510262,"traceId":"027b520ae38c0c54"},{"name":"build-module-css","duration":13261,"timestamp":3307505761349,"id":135,"parentId":125,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/src/app/globals.css","layer":"rsc"},"startTime":1750786510261,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":15634,"timestamp":3307505762466,"id":144,"parentId":127,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/app-render/collect-segment-data.js","layer":"rsc"},"startTime":1750786510262,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6080,"timestamp":3307505772357,"id":149,"parentId":127,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/metadata/metadata-boundary.js","layer":"rsc"},"startTime":1750786510272,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13049,"timestamp":3307505772231,"id":147,"parentId":127,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/request/params.js","layer":"rsc"},"startTime":1750786510272,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":18741,"timestamp":3307505772128,"id":146,"parentId":127,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/request/search-params.js","layer":"rsc"},"startTime":1750786510272,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":19117,"timestamp":3307505772383,"id":150,"parentId":127,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/app-render/rsc/preloads.js","layer":"rsc"},"startTime":1750786510272,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":19827,"timestamp":3307505772486,"id":152,"parentId":127,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/app-render/rsc/taint.js","layer":"rsc"},"startTime":1750786510272,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":20058,"timestamp":3307505772435,"id":151,"parentId":127,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/app-render/rsc/postpone.js","layer":"rsc"},"startTime":1750786510272,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":26551,"timestamp":3307505772299,"id":148,"parentId":127,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/lib/patch-fetch.js","layer":"rsc"},"startTime":1750786510272,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":27385,"timestamp":3307505778742,"id":153,"parentId":127,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/metadata.js","layer":"rsc"},"startTime":1750786510279,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":32076,"timestamp":3307505780973,"id":154,"parentId":123,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/get-metadata-route.js","layer":"rsc"},"startTime":1750786510281,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10409,"timestamp":3307505806669,"id":155,"parentId":144,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/scheduler.js","layer":"rsc"},"startTime":1750786510306,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11445,"timestamp":3307505806778,"id":156,"parentId":144,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/app-render/create-error-handler.js","layer":"rsc"},"startTime":1750786510307,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":12619,"timestamp":3307505811307,"id":157,"parentId":128,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js","layer":"rsc"},"startTime":1750786510311,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6959,"timestamp":3307505819491,"id":171,"parentId":153,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/metadata/async-metadata.js","layer":"rsc"},"startTime":1750786510319,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7534,"timestamp":3307505819466,"id":170,"parentId":153,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js","layer":"rsc"},"startTime":1750786510319,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":16329,"timestamp":3307505813601,"id":158,"parentId":144,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/stream-utils/node-web-streams-helper.js","layer":"rsc"},"startTime":1750786510313,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":16877,"timestamp":3307505813696,"id":159,"parentId":147,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/dynamic-rendering-utils.js","layer":"rsc"},"startTime":1750786510313,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":17957,"timestamp":3307505813759,"id":160,"parentId":147,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js","layer":"rsc"},"startTime":1750786510314,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":21053,"timestamp":3307505813861,"id":162,"parentId":147,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/app-render/dynamic-rendering.js","layer":"rsc"},"startTime":1750786510314,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":21419,"timestamp":3307505813912,"id":163,"parentId":146,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/request/utils.js","layer":"rsc"},"startTime":1750786510314,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":22504,"timestamp":3307505813812,"id":161,"parentId":148,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/constants.js","layer":"rsc"},"startTime":1750786510314,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":23060,"timestamp":3307505813961,"id":164,"parentId":148,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/lib/dedupe-fetch.js","layer":"rsc"},"startTime":1750786510314,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":23262,"timestamp":3307505814009,"id":165,"parentId":148,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/lib/clone-response.js","layer":"rsc"},"startTime":1750786510314,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":12461,"timestamp":3307505825152,"id":172,"parentId":147,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/invariant-error.js","layer":"rsc"},"startTime":1750786510325,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13016,"timestamp":3307505825235,"id":174,"parentId":147,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/utils/reflect-utils.js","layer":"rsc"},"startTime":1750786510325,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":18696,"timestamp":3307505825206,"id":173,"parentId":144,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/segment-cache/segment-value-encoding.js","layer":"rsc"},"startTime":1750786510325,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7081,"timestamp":3307505839937,"id":186,"parentId":154,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/hash.js","layer":"rsc"},"startTime":1750786510340,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7315,"timestamp":3307505839959,"id":187,"parentId":154,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/segment.js","layer":"rsc"},"startTime":1750786510340,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":29565,"timestamp":3307505819209,"id":166,"parentId":148,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/lib/trace/constants.js","layer":"rsc"},"startTime":1750786510319,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":29557,"timestamp":3307505819413,"id":169,"parentId":153,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/metadata-constants.js","layer":"rsc"},"startTime":1750786510319,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":32861,"timestamp":3307505819358,"id":168,"parentId":153,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/resolve-metadata.js","layer":"rsc"},"startTime":1750786510319,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":34417,"timestamp":3307505819298,"id":167,"parentId":148,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/lib/trace/tracer.js","layer":"rsc"},"startTime":1750786510319,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6553,"timestamp":3307505854560,"id":190,"parentId":154,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/page-path/normalize-path-sep.js","layer":"rsc"},"startTime":1750786510354,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6770,"timestamp":3307505854520,"id":189,"parentId":154,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/isomorphic/path.js","layer":"rsc"},"startTime":1750786510354,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6694,"timestamp":3307505854881,"id":195,"parentId":156,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/is-next-router-error.js","layer":"rsc"},"startTime":1750786510355,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":22957,"timestamp":3307505839490,"id":177,"parentId":129,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js","layer":"rsc"},"startTime":1750786510339,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":23252,"timestamp":3307505839598,"id":179,"parentId":153,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/generate/alternate.js","layer":"rsc"},"startTime":1750786510339,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":23655,"timestamp":3307505839320,"id":175,"parentId":127,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js","layer":"rsc"},"startTime":1750786510339,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":23660,"timestamp":3307505839426,"id":176,"parentId":127,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-static-edge.js","layer":"rsc"},"startTime":1750786510339,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":23457,"timestamp":3307505839793,"id":183,"parentId":153,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/lib/router-utils/is-postpone.js","layer":"rsc"},"startTime":1750786510340,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":23861,"timestamp":3307505839697,"id":181,"parentId":153,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/generate/icons.js","layer":"rsc"},"startTime":1750786510339,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":24380,"timestamp":3307505839746,"id":182,"parentId":153,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/generate/meta.js","layer":"rsc"},"startTime":1750786510340,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":25100,"timestamp":3307505839884,"id":185,"parentId":154,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/is-metadata-route.js","layer":"rsc"},"startTime":1750786510340,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":26469,"timestamp":3307505839840,"id":184,"parentId":154,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/server-utils.js","layer":"rsc"},"startTime":1750786510340,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":27726,"timestamp":3307505839648,"id":180,"parentId":153,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/generate/opengraph.js","layer":"rsc"},"startTime":1750786510339,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":28876,"timestamp":3307505839546,"id":178,"parentId":153,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/generate/basic.js","layer":"rsc"},"startTime":1750786510339,"traceId":"027b520ae38c0c54"},{"name":"build-module-external","duration":16,"timestamp":3307505870261,"id":205,"parentId":163,"tags":{"name":"../app-render/after-task-async-storage.external","layer":null},"startTime":1750786510370,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7917,"timestamp":3307505869985,"id":200,"parentId":156,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js","layer":"rsc"},"startTime":1750786510370,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8116,"timestamp":3307505870237,"id":204,"parentId":162,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/static-generation-bailout.js","layer":"rsc"},"startTime":1750786510370,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8807,"timestamp":3307505869956,"id":199,"parentId":154,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/app-paths.js","layer":"rsc"},"startTime":1750786510370,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10200,"timestamp":3307505869920,"id":198,"parentId":154,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/route-regex.js","layer":"rsc"},"startTime":1750786510370,"traceId":"027b520ae38c0c54"},{"name":"build-module","duration":26,"timestamp":3307505880385,"id":206,"parentId":189,"tags":{"name":"path","layer":null},"startTime":1750786510380,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":27419,"timestamp":3307505854412,"id":188,"parentId":147,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/web/spec-extension/adapters/reflect.js","layer":"rsc"},"startTime":1750786510354,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":27597,"timestamp":3307505854760,"id":193,"parentId":156,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/is-error.js","layer":"rsc"},"startTime":1750786510355,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":28259,"timestamp":3307505854673,"id":192,"parentId":156,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/pipe-readable.js","layer":"rsc"},"startTime":1750786510354,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":28709,"timestamp":3307505854820,"id":194,"parentId":156,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/error-telemetry-utils.js","layer":"rsc"},"startTime":1750786510355,"traceId":"027b520ae38c0c54"},{"name":"build-module","duration":20,"timestamp":3307505883989,"id":207,"parentId":184,"tags":{"name":"url","layer":null},"startTime":1750786510384,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":32990,"timestamp":3307505854588,"id":191,"parentId":156,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/format-server-error.js","layer":"rsc"},"startTime":1750786510354,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":25993,"timestamp":3307505861766,"id":196,"parentId":125,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js","layer":"rsc"},"startTime":1750786510362,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5841,"timestamp":3307505884016,"id":208,"parentId":158,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/errors/constants.js","layer":"rsc"},"startTime":1750786510384,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5537,"timestamp":3307505884456,"id":215,"parentId":167,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/is-thenable.js","layer":"rsc"},"startTime":1750786510384,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5851,"timestamp":3307505884318,"id":212,"parentId":168,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/deep-freeze.js","layer":"rsc"},"startTime":1750786510384,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":22736,"timestamp":3307505870180,"id":203,"parentId":158,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/stream-utils/uint8array-helpers.js","layer":"rsc"},"startTime":1750786510370,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":23180,"timestamp":3307505870012,"id":201,"parentId":158,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/detached-promise.js","layer":"rsc"},"startTime":1750786510370,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":23305,"timestamp":3307505870072,"id":202,"parentId":158,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/stream-utils/encodedTags.js","layer":"rsc"},"startTime":1750786510370,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":23669,"timestamp":3307505869810,"id":197,"parentId":153,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js","layer":"rsc"},"startTime":1750786510370,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3945,"timestamp":3307505891962,"id":217,"parentId":195,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/redirect-error.js","layer":"rsc"},"startTime":1750786510392,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2840,"timestamp":3307505894130,"id":229,"parentId":184,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/remove-trailing-slash.js","layer":"rsc"},"startTime":1750786510394,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3202,"timestamp":3307505894050,"id":226,"parentId":184,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/path-match.js","layer":"rsc"},"startTime":1750786510394,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3556,"timestamp":3307505894105,"id":228,"parentId":184,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/route-matcher.js","layer":"rsc"},"startTime":1750786510394,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13662,"timestamp":3307505884262,"id":211,"parentId":168,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/lib/app-dir-module.js","layer":"rsc"},"startTime":1750786510384,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13953,"timestamp":3307505884084,"id":209,"parentId":168,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/interop-default.js","layer":"rsc"},"startTime":1750786510384,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13849,"timestamp":3307505884341,"id":213,"parentId":168,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/clone-metadata.js","layer":"rsc"},"startTime":1750786510384,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13982,"timestamp":3307505884399,"id":214,"parentId":168,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/generate/utils.js","layer":"rsc"},"startTime":1750786510384,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":14361,"timestamp":3307505884196,"id":210,"parentId":168,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/default-metadata.js","layer":"rsc"},"startTime":1750786510384,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5544,"timestamp":3307505894077,"id":227,"parentId":184,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/prepare-destination.js","layer":"rsc"},"startTime":1750786510394,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1472,"timestamp":3307505899898,"id":233,"parentId":199,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/page-path/ensure-leading-slash.js","layer":"rsc"},"startTime":1750786510400,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1691,"timestamp":3307505899850,"id":232,"parentId":198,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/escape-regexp.js","layer":"rsc"},"startTime":1750786510400,"traceId":"027b520ae38c0c54"}]
[{"name":"build-module-js","duration":10615,"timestamp":3307505891866,"id":216,"parentId":148,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/response-cache/index.js","layer":"rsc"},"startTime":1750786510392,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9723,"timestamp":3307505893592,"id":218,"parentId":168,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/resolvers/resolve-opengraph.js","layer":"rsc"},"startTime":1750786510393,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9894,"timestamp":3307505893669,"id":219,"parentId":168,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/resolvers/resolve-title.js","layer":"rsc"},"startTime":1750786510393,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9803,"timestamp":3307505893885,"id":223,"parentId":185,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/is-app-route-route.js","layer":"rsc"},"startTime":1750786510394,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10139,"timestamp":3307505893782,"id":221,"parentId":168,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/resolvers/resolve-icons.js","layer":"rsc"},"startTime":1750786510394,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10846,"timestamp":3307505893727,"id":220,"parentId":168,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/resolvers/resolve-basics.js","layer":"rsc"},"startTime":1750786510394,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11286,"timestamp":3307505893835,"id":222,"parentId":168,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/output/log.js","layer":"rsc"},"startTime":1750786510394,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11632,"timestamp":3307505893941,"id":224,"parentId":184,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/web/utils.js","layer":"rsc"},"startTime":1750786510394,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11578,"timestamp":3307505894153,"id":230,"parentId":178,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/constants.js","layer":"rsc"},"startTime":1750786510394,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11849,"timestamp":3307505893997,"id":225,"parentId":182,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/non-nullable.js","layer":"rsc"},"startTime":1750786510394,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8803,"timestamp":3307505899925,"id":234,"parentId":198,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/interception-routes.js","layer":"rsc"},"startTime":1750786510400,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8961,"timestamp":3307505899968,"id":236,"parentId":193,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/is-plain-object.js","layer":"rsc"},"startTime":1750786510400,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10298,"timestamp":3307505899947,"id":235,"parentId":184,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/i18n/normalize-locale-path.js","layer":"rsc"},"startTime":1750786510400,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":14178,"timestamp":3307505896647,"id":231,"parentId":144,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/react-server-dom-webpack/client.edge.js","layer":"rsc"},"startTime":1750786510396,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":12089,"timestamp":3307505899987,"id":237,"parentId":192,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/client-component-renderer-logger.js","layer":"rsc"},"startTime":1750786510400,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":12764,"timestamp":3307505900040,"id":238,"parentId":192,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/web/spec-extension/adapters/next-request.js","layer":"rsc"},"startTime":1750786510400,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4989,"timestamp":3307505911324,"id":239,"parentId":217,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/redirect-status-code.js","layer":"rsc"},"startTime":1750786510411,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2154,"timestamp":3307505915586,"id":241,"parentId":227,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/parse-url.js","layer":"rsc"},"startTime":1750786510415,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2952,"timestamp":3307505915529,"id":240,"parentId":228,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/utils.js","layer":"rsc"},"startTime":1750786510415,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5834,"timestamp":3307505917146,"id":244,"parentId":222,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/picocolors.js","layer":"rsc"},"startTime":1750786510417,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6294,"timestamp":3307505917071,"id":243,"parentId":216,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/batcher.js","layer":"rsc"},"startTime":1750786510417,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6884,"timestamp":3307505917318,"id":247,"parentId":218,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/url.js","layer":"rsc"},"startTime":1750786510417,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7516,"timestamp":3307505917208,"id":245,"parentId":216,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/response-cache/utils.js","layer":"rsc"},"startTime":1750786510417,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7711,"timestamp":3307505917262,"id":246,"parentId":216,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/response-cache/types.js","layer":"rsc"},"startTime":1750786510417,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8149,"timestamp":3307505916986,"id":242,"parentId":150,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-dom.js","layer":"rsc"},"startTime":1750786510417,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8493,"timestamp":3307505917367,"id":248,"parentId":222,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/lib/lru-cache.js","layer":"rsc"},"startTime":1750786510417,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9237,"timestamp":3307505917419,"id":249,"parentId":218,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/resolvers/resolve-url.js","layer":"rsc"},"startTime":1750786510417,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8241,"timestamp":3307505918615,"id":250,"parentId":227,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/api-utils/get-cookie-parser.js","layer":"rsc"},"startTime":1750786510418,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":20664,"timestamp":3307505918687,"id":251,"parentId":167,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/@opentelemetry/api/index.js","layer":"rsc"},"startTime":1750786510418,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10372,"timestamp":3307505929500,"id":257,"parentId":241,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/parse-relative-url.js","layer":"rsc"},"startTime":1750786510429,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10877,"timestamp":3307505929433,"id":256,"parentId":241,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/querystring.js","layer":"rsc"},"startTime":1750786510429,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":20715,"timestamp":3307505920631,"id":252,"parentId":238,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/request-meta.js","layer":"rsc"},"startTime":1750786510420,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":21359,"timestamp":3307505920726,"id":253,"parentId":238,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/web/spec-extension/request.js","layer":"rsc"},"startTime":1750786510421,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":18283,"timestamp":3307505929234,"id":255,"parentId":238,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/base-http/helpers.js","layer":"rsc"},"startTime":1750786510429,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":15353,"timestamp":3307505940861,"id":258,"parentId":156,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/string-hash/index.js","layer":"rsc"},"startTime":1750786510441,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":46522,"timestamp":3307505920994,"id":254,"parentId":231,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.edge.development.js","layer":"rsc"},"startTime":1750786510421,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":14983,"timestamp":3307505952912,"id":260,"parentId":247,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/app-router-headers.js","layer":"rsc"},"startTime":1750786510453,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":17328,"timestamp":3307505952413,"id":259,"parentId":134,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/@swc/helpers/esm/_interop_require_default.js","layer":"rsc"},"startTime":1750786510452,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":17330,"timestamp":3307505952964,"id":261,"parentId":245,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/render-result.js","layer":"rsc"},"startTime":1750786510453,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":15357,"timestamp":3307505956323,"id":262,"parentId":168,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/server-only/empty.js","layer":"rsc"},"startTime":1750786510456,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3921,"timestamp":3307505968248,"id":265,"parentId":253,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/web/spec-extension/cookies.js","layer":"rsc"},"startTime":1750786510468,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4367,"timestamp":3307505968188,"id":264,"parentId":253,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/web/error.js","layer":"rsc"},"startTime":1750786510468,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5271,"timestamp":3307505968071,"id":263,"parentId":253,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/web/next-url.js","layer":"rsc"},"startTime":1750786510468,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2812,"timestamp":3307505973480,"id":266,"parentId":226,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/path-to-regexp/index.js","layer":"rsc"},"startTime":1750786510473,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1988,"timestamp":3307505974554,"id":267,"parentId":263,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/get-hostname.js","layer":"rsc"},"startTime":1750786510474,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2132,"timestamp":3307505974598,"id":268,"parentId":263,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/i18n/detect-domain-locale.js","layer":"rsc"},"startTime":1750786510474,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2298,"timestamp":3307505974626,"id":269,"parentId":263,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/format-next-pathname-info.js","layer":"rsc"},"startTime":1750786510474,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2575,"timestamp":3307505974650,"id":270,"parentId":263,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/get-next-pathname-info.js","layer":"rsc"},"startTime":1750786510474,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1653,"timestamp":3307505979281,"id":271,"parentId":269,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/add-path-prefix.js","layer":"rsc"},"startTime":1750786510479,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1830,"timestamp":3307505979347,"id":272,"parentId":269,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/add-path-suffix.js","layer":"rsc"},"startTime":1750786510479,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1911,"timestamp":3307505979592,"id":274,"parentId":270,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/remove-path-prefix.js","layer":"rsc"},"startTime":1750786510479,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2442,"timestamp":3307505979378,"id":273,"parentId":269,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/add-locale.js","layer":"rsc"},"startTime":1750786510479,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2304,"timestamp":3307505979643,"id":275,"parentId":270,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/path-has-prefix.js","layer":"rsc"},"startTime":1750786510479,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3269,"timestamp":3307505979668,"id":276,"parentId":250,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/cookie/index.js","layer":"rsc"},"startTime":1750786510479,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":988,"timestamp":3307505982977,"id":277,"parentId":271,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/parse-path.js","layer":"rsc"},"startTime":1750786510483,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1862,"timestamp":3307505983725,"id":278,"parentId":265,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/@edge-runtime/cookies/index.js","layer":"rsc"},"startTime":1750786510484,"traceId":"027b520ae38c0c54"},{"name":"add-entry","duration":340296,"timestamp":3307505645379,"id":121,"parentId":120,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750786510145,"traceId":"027b520ae38c0c54"},{"name":"build-module","duration":1091,"timestamp":3307506003120,"id":286,"parentId":119,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!","layer":"ssr"},"startTime":1750786510503,"traceId":"027b520ae38c0c54"},{"name":"build-module","duration":66,"timestamp":3307506004225,"id":287,"parentId":119,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!","layer":"rsc"},"startTime":1750786510504,"traceId":"027b520ae38c0c54"},{"name":"build-module","duration":46,"timestamp":3307506004297,"id":288,"parentId":119,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!","layer":"ssr"},"startTime":1750786510504,"traceId":"027b520ae38c0c54"},{"name":"build-module","duration":36,"timestamp":3307506004347,"id":289,"parentId":119,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!","layer":"rsc"},"startTime":1750786510504,"traceId":"027b520ae38c0c54"},{"name":"build-module","duration":755,"timestamp":3307506004386,"id":290,"parentId":119,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!","layer":"ssr"},"startTime":1750786510504,"traceId":"027b520ae38c0c54"},{"name":"build-module","duration":194,"timestamp":3307506005148,"id":291,"parentId":119,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!","layer":"rsc"},"startTime":1750786510505,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2211,"timestamp":3307506010685,"id":293,"parentId":290,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/client-page.js","layer":"ssr"},"startTime":1750786510510,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4125,"timestamp":3307506010716,"id":294,"parentId":290,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/client-segment.js","layer":"ssr"},"startTime":1750786510511,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4273,"timestamp":3307506010756,"id":296,"parentId":290,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/render-from-template-context.js","layer":"ssr"},"startTime":1750786510511,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5948,"timestamp":3307506009869,"id":292,"parentId":290,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/error-boundary.js","layer":"ssr"},"startTime":1750786510510,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5459,"timestamp":3307506010794,"id":298,"parentId":290,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/metadata/async-metadata.js","layer":"ssr"},"startTime":1750786510511,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6042,"timestamp":3307506010776,"id":297,"parentId":290,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","layer":"ssr"},"startTime":1750786510511,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6251,"timestamp":3307506010811,"id":299,"parentId":290,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/metadata/metadata-boundary.js","layer":"ssr"},"startTime":1750786510511,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7662,"timestamp":3307506010737,"id":295,"parentId":290,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/layout-router.js","layer":"ssr"},"startTime":1750786510511,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1571,"timestamp":3307506023383,"id":301,"parentId":293,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/request/search-params.js","layer":"ssr"},"startTime":1750786510523,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2336,"timestamp":3307506023434,"id":302,"parentId":293,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/request/params.js","layer":"ssr"},"startTime":1750786510523,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2860,"timestamp":3307506023322,"id":300,"parentId":293,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/invariant-error.js","layer":"ssr"},"startTime":1750786510523,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1364,"timestamp":3307506026534,"id":303,"parentId":292,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/@swc/helpers/esm/_interop_require_default.js","layer":"ssr"},"startTime":1750786510526,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1224,"timestamp":3307506026810,"id":305,"parentId":299,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/metadata-constants.js","layer":"ssr"},"startTime":1750786510527,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5516,"timestamp":3307506026785,"id":304,"parentId":292,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/is-next-router-error.js","layer":"ssr"},"startTime":1750786510527,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5733,"timestamp":3307506026861,"id":306,"parentId":297,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js","layer":"ssr"},"startTime":1750786510527,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1545,"timestamp":3307506035536,"id":307,"parentId":301,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/dynamic-rendering-utils.js","layer":"ssr"},"startTime":1750786510535,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2143,"timestamp":3307506035614,"id":308,"parentId":301,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js","layer":"ssr"},"startTime":1750786510535,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2523,"timestamp":3307506035665,"id":309,"parentId":301,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/scheduler.js","layer":"ssr"},"startTime":1750786510535,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3816,"timestamp":3307506035710,"id":310,"parentId":301,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/app-render/dynamic-rendering.js","layer":"ssr"},"startTime":1750786510536,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3977,"timestamp":3307506035757,"id":311,"parentId":301,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/request/utils.js","layer":"ssr"},"startTime":1750786510536,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4045,"timestamp":3307506035821,"id":313,"parentId":301,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/web/spec-extension/adapters/reflect.js","layer":"ssr"},"startTime":1750786510536,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5247,"timestamp":3307506035802,"id":312,"parentId":301,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/utils/reflect-utils.js","layer":"ssr"},"startTime":1750786510536,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5320,"timestamp":3307506035900,"id":316,"parentId":295,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/unresolved-thenable.js","layer":"ssr"},"startTime":1750786510536,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5666,"timestamp":3307506035883,"id":315,"parentId":292,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/nav-failure-handler.js","layer":"ssr"},"startTime":1750786510536,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5881,"timestamp":3307506035866,"id":314,"parentId":292,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/navigation-untracked.js","layer":"ssr"},"startTime":1750786510536,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5958,"timestamp":3307506035951,"id":317,"parentId":295,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/match-segments.js","layer":"ssr"},"startTime":1750786510536,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5993,"timestamp":3307506036022,"id":321,"parentId":297,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/utils/warn-once.js","layer":"ssr"},"startTime":1750786510536,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6281,"timestamp":3307506035986,"id":319,"parentId":295,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/use-action-queue.js","layer":"ssr"},"startTime":1750786510536,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6529,"timestamp":3307506036006,"id":320,"parentId":298,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/metadata/server-inserted-metadata.js","layer":"ssr"},"startTime":1750786510536,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6894,"timestamp":3307506035969,"id":318,"parentId":295,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/redirect-boundary.js","layer":"ssr"},"startTime":1750786510536,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6946,"timestamp":3307506036037,"id":322,"parentId":295,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js","layer":"ssr"},"startTime":1750786510536,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3133,"timestamp":3307506043928,"id":323,"parentId":304,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/redirect-error.js","layer":"ssr"},"startTime":1750786510544,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1163,"timestamp":3307506048176,"id":327,"parentId":295,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js","layer":"ssr"},"startTime":1750786510548,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2654,"timestamp":3307506048156,"id":326,"parentId":295,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/create-router-cache-key.js","layer":"ssr"},"startTime":1750786510548,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2994,"timestamp":3307506048087,"id":324,"parentId":295,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/router-reducer-types.js","layer":"ssr"},"startTime":1750786510548,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2466,"timestamp":3307506048837,"id":328,"parentId":310,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/hooks-server-context.js","layer":"ssr"},"startTime":1750786510549,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2609,"timestamp":3307506048904,"id":330,"parentId":319,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/is-thenable.js","layer":"ssr"},"startTime":1750786510549,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2874,"timestamp":3307506048879,"id":329,"parentId":310,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/static-generation-bailout.js","layer":"ssr"},"startTime":1750786510549,"traceId":"027b520ae38c0c54"}]
[{"name":"build-module-js","duration":4709,"timestamp":3307506048133,"id":325,"parentId":295,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/fetch-server-response.js","layer":"ssr"},"startTime":1750786510548,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3248,"timestamp":3307506049727,"id":331,"parentId":323,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/redirect-status-code.js","layer":"ssr"},"startTime":1750786510550,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1121,"timestamp":3307506054478,"id":332,"parentId":327,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/interception-routes.js","layer":"ssr"},"startTime":1750786510554,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2292,"timestamp":3307506054944,"id":336,"parentId":326,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/segment.js","layer":"ssr"},"startTime":1750786510555,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2817,"timestamp":3307506054560,"id":335,"parentId":315,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/create-href-from-url.js","layer":"ssr"},"startTime":1750786510554,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5880,"timestamp":3307506054517,"id":333,"parentId":318,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/redirect.js","layer":"ssr"},"startTime":1750786510554,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5715,"timestamp":3307506054968,"id":337,"parentId":325,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/app-router-headers.js","layer":"ssr"},"startTime":1750786510555,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6659,"timestamp":3307506054540,"id":334,"parentId":318,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/navigation.js","layer":"ssr"},"startTime":1750786510554,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2425,"timestamp":3307506062804,"id":341,"parentId":325,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/flight-data-helpers.js","layer":"ssr"},"startTime":1750786510563,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2665,"timestamp":3307506062757,"id":339,"parentId":325,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/app-call-server.js","layer":"ssr"},"startTime":1750786510563,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2865,"timestamp":3307506062783,"id":340,"parentId":325,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/app-find-source-map-url.js","layer":"ssr"},"startTime":1750786510563,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4376,"timestamp":3307506062704,"id":338,"parentId":332,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/app-paths.js","layer":"ssr"},"startTime":1750786510563,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4466,"timestamp":3307506062823,"id":342,"parentId":325,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/app-build-id.js","layer":"ssr"},"startTime":1750786510563,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4659,"timestamp":3307506062842,"id":343,"parentId":325,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/set-cache-busting-search-param.js","layer":"ssr"},"startTime":1750786510563,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2612,"timestamp":3307506065938,"id":348,"parentId":319,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator.js","layer":"ssr"},"startTime":1750786510566,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5973,"timestamp":3307506065907,"id":347,"parentId":325,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js","layer":"ssr"},"startTime":1750786510566,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10624,"timestamp":3307506064685,"id":344,"parentId":293,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js","layer":"ssr"},"startTime":1750786510564,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10669,"timestamp":3307506064755,"id":345,"parentId":295,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js","layer":"ssr"},"startTime":1750786510565,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10740,"timestamp":3307506064823,"id":346,"parentId":296,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js","layer":"ssr"},"startTime":1750786510565,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3419,"timestamp":3307506072537,"id":350,"parentId":338,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/page-path/ensure-leading-slash.js","layer":"ssr"},"startTime":1750786510572,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3626,"timestamp":3307506072494,"id":349,"parentId":343,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/hash.js","layer":"ssr"},"startTime":1750786510572,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3919,"timestamp":3307506072586,"id":352,"parentId":334,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/bailout-to-client-rendering.js","layer":"ssr"},"startTime":1750786510572,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4013,"timestamp":3307506072615,"id":353,"parentId":334,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/reducers/get-segment-value.js","layer":"ssr"},"startTime":1750786510572,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4324,"timestamp":3307506072563,"id":351,"parentId":334,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/navigation.react-server.js","layer":"ssr"},"startTime":1750786510572,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":560,"timestamp":3307506081831,"id":357,"parentId":344,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/route-modules/app-page/module.compiled.js","layer":"ssr"},"startTime":1750786510582,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10996,"timestamp":3307506072635,"id":354,"parentId":296,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/route-modules/app-page/vendored/contexts/app-router-context.js","layer":"ssr"},"startTime":1750786510572,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3438,"timestamp":3307506081793,"id":356,"parentId":352,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js","layer":"ssr"},"startTime":1750786510582,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3785,"timestamp":3307506081995,"id":360,"parentId":348,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/dev-render-indicator.js","layer":"ssr"},"startTime":1750786510582,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3856,"timestamp":3307506083761,"id":363,"parentId":347,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/parse-stack.js","layer":"ssr"},"startTime":1750786510584,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4305,"timestamp":3307506083779,"id":364,"parentId":347,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js","layer":"ssr"},"startTime":1750786510584,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5249,"timestamp":3307506083739,"id":362,"parentId":347,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/format-webpack-messages.js","layer":"ssr"},"startTime":1750786510584,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5805,"timestamp":3307506083707,"id":361,"parentId":347,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/shared.js","layer":"ssr"},"startTime":1750786510584,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6038,"timestamp":3307506083815,"id":366,"parentId":347,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/parse-component-stack.js","layer":"ssr"},"startTime":1750786510584,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6485,"timestamp":3307506083797,"id":365,"parentId":347,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/use-websocket.js","layer":"ssr"},"startTime":1750786510584,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6647,"timestamp":3307506083833,"id":367,"parentId":347,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/report-hmr-latency.js","layer":"ssr"},"startTime":1750786510584,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7095,"timestamp":3307506083851,"id":368,"parentId":347,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/turbopack-hot-reloader-common.js","layer":"ssr"},"startTime":1750786510584,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9961,"timestamp":3307506086705,"id":370,"parentId":351,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/not-found.js","layer":"ssr"},"startTime":1750786510587,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10106,"timestamp":3307506086770,"id":373,"parentId":351,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/unstable-rethrow.js","layer":"ssr"},"startTime":1750786510587,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10352,"timestamp":3307506086751,"id":372,"parentId":351,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/unauthorized.js","layer":"ssr"},"startTime":1750786510587,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10561,"timestamp":3307506086731,"id":371,"parentId":351,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/forbidden.js","layer":"ssr"},"startTime":1750786510587,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":16948,"timestamp":3307506080460,"id":355,"parentId":314,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/route-modules/app-page/vendored/contexts/hooks-client-context.js","layer":"ssr"},"startTime":1750786510580,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":15709,"timestamp":3307506081886,"id":358,"parentId":320,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/route-modules/app-page/vendored/contexts/server-inserted-metadata.js","layer":"ssr"},"startTime":1750786510582,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":15750,"timestamp":3307506081942,"id":359,"parentId":325,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-server-dom-webpack-client-edge.js","layer":"ssr"},"startTime":1750786510582,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6325,"timestamp":3307506091584,"id":375,"parentId":347,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/errors/runtime-error-handler.js","layer":"ssr"},"startTime":1750786510591,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6637,"timestamp":3307506091605,"id":376,"parentId":347,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/errors/stitched-error.js","layer":"ssr"},"startTime":1750786510591,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7225,"timestamp":3307506091548,"id":374,"parentId":347,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/errors/use-error-handler.js","layer":"ssr"},"startTime":1750786510591,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":16783,"timestamp":3307506086633,"id":369,"parentId":296,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/@swc/helpers/esm/_interop_require_wildcard.js","layer":"ssr"},"startTime":1750786510586,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":465,"timestamp":3307506103786,"id":379,"parentId":376,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/is-error.js","layer":"ssr"},"startTime":1750786510604,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":581,"timestamp":3307506103865,"id":380,"parentId":376,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/error-telemetry-utils.js","layer":"ssr"},"startTime":1750786510604,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2607,"timestamp":3307506102236,"id":378,"parentId":364,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/errors/constants.js","layer":"ssr"},"startTime":1750786510602,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13457,"timestamp":3307506091626,"id":377,"parentId":347,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/dev/hot-reloader-types.js","layer":"ssr"},"startTime":1750786510591,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2770,"timestamp":3307506105987,"id":381,"parentId":347,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.js","layer":"ssr"},"startTime":1750786510606,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3015,"timestamp":3307506106073,"id":384,"parentId":365,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/get-socket-url.js","layer":"ssr"},"startTime":1750786510606,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4036,"timestamp":3307506106029,"id":382,"parentId":363,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/is-hydration-error.js","layer":"ssr"},"startTime":1750786510606,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4380,"timestamp":3307506106053,"id":383,"parentId":364,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js","layer":"ssr"},"startTime":1750786510606,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7271,"timestamp":3307506107246,"id":387,"parentId":374,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/errors/attach-hydration-error-state.js","layer":"ssr"},"startTime":1750786510607,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7487,"timestamp":3307506107317,"id":389,"parentId":374,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/errors/console-error.js","layer":"ssr"},"startTime":1750786510607,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7793,"timestamp":3307506107211,"id":386,"parentId":373,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/unstable-rethrow.server.js","layer":"ssr"},"startTime":1750786510607,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8375,"timestamp":3307506107269,"id":388,"parentId":374,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/errors/hydration-error-info.js","layer":"ssr"},"startTime":1750786510607,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8450,"timestamp":3307506107360,"id":390,"parentId":374,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/errors/enqueue-client-error.js","layer":"ssr"},"startTime":1750786510607,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8304,"timestamp":3307506107646,"id":391,"parentId":379,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/is-plain-object.js","layer":"ssr"},"startTime":1750786510607,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8878,"timestamp":3307506107673,"id":392,"parentId":364,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js","layer":"ssr"},"startTime":1750786510607,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9141,"timestamp":3307506107693,"id":393,"parentId":364,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/dev-overlay.js","layer":"ssr"},"startTime":1750786510607,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3782,"timestamp":3307506117551,"id":394,"parentId":374,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/lib/console.js","layer":"ssr"},"startTime":1750786510617,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":15119,"timestamp":3307506107113,"id":385,"parentId":334,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/route-modules/app-page/vendored/contexts/server-inserted-html.js","layer":"ssr"},"startTime":1750786510607,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":335,"timestamp":3307506122749,"id":395,"parentId":386,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/lib/router-utils/is-postpone.js","layer":"ssr"},"startTime":1750786510623,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1485,"timestamp":3307506123514,"id":397,"parentId":381,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js","layer":"ssr"},"startTime":1750786510623,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1672,"timestamp":3307506123475,"id":396,"parentId":384,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/normalized-asset-prefix.js","layer":"ssr"},"startTime":1750786510623,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1591,"timestamp":3307506123759,"id":398,"parentId":392,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/css.js","layer":"ssr"},"startTime":1750786510624,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1631,"timestamp":3307506126286,"id":400,"parentId":393,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/styles/base.js","layer":"ssr"},"startTime":1750786510626,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1875,"timestamp":3307506126334,"id":402,"parentId":393,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/styles/css-reset.js","layer":"ssr"},"startTime":1750786510626,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2524,"timestamp":3307506126228,"id":399,"parentId":393,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/shadow-portal.js","layer":"ssr"},"startTime":1750786510626,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6948,"timestamp":3307506126313,"id":401,"parentId":393,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/styles/component-styles.js","layer":"ssr"},"startTime":1750786510626,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7145,"timestamp":3307506126369,"id":404,"parentId":393,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/styles/dark-theme.js","layer":"ssr"},"startTime":1750786510626,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7392,"timestamp":3307506126352,"id":403,"parentId":393,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/styles/colors.js","layer":"ssr"},"startTime":1750786510626,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5140,"timestamp":3307506129284,"id":407,"parentId":393,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/preferences.js","layer":"ssr"},"startTime":1750786510629,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5483,"timestamp":3307506129210,"id":405,"parentId":393,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay/error-overlay.js","layer":"ssr"},"startTime":1750786510629,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5747,"timestamp":3307506129305,"id":408,"parentId":393,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/render-error.js","layer":"ssr"},"startTime":1750786510629,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6758,"timestamp":3307506129260,"id":406,"parentId":393,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-indicator.js","layer":"ssr"},"startTime":1750786510629,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3721,"timestamp":3307506144963,"id":409,"parentId":365,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/dev/noop-turbopack-hmr.js","layer":"ssr"},"startTime":1750786510645,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3733,"timestamp":3307506145951,"id":410,"parentId":397,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js","layer":"ssr"},"startTime":1750786510646,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5107,"timestamp":3307506148759,"id":411,"parentId":347,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/strip-ansi/index.js","layer":"ssr"},"startTime":1750786510649,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4441,"timestamp":3307506149893,"id":414,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js","layer":"ssr"},"startTime":1750786510650,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6266,"timestamp":3307506149829,"id":412,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/container/build-error.js","layer":"ssr"},"startTime":1750786510650,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6612,"timestamp":3307506149914,"id":415,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/turbopack-info.js","layer":"ssr"},"startTime":1750786510650,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7277,"timestamp":3307506149869,"id":413,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/container/errors.js","layer":"ssr"},"startTime":1750786510650,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7511,"timestamp":3307506149990,"id":419,"parentId":408,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/get-error-by-type.js","layer":"ssr"},"startTime":1750786510650,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9284,"timestamp":3307506149934,"id":416,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/route-info.js","layer":"ssr"},"startTime":1750786510650,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10369,"timestamp":3307506149954,"id":417,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.js","layer":"ssr"},"startTime":1750786510650,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11379,"timestamp":3307506149972,"id":418,"parentId":406,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/next-logo.js","layer":"ssr"},"startTime":1750786510650,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":12020,"timestamp":3307506150007,"id":420,"parentId":406,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js","layer":"ssr"},"startTime":1750786510650,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9012,"timestamp":3307506163381,"id":423,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/code-frame/code-frame.js","layer":"ssr"},"startTime":1750786510663,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9253,"timestamp":3307506163402,"id":424,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/editor-link.js","layer":"ssr"},"startTime":1750786510663,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9864,"timestamp":3307506163281,"id":421,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-layout/error-overlay-layout.js","layer":"ssr"},"startTime":1750786510663,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11118,"timestamp":3307506163354,"id":422,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-pagination/error-overlay-pagination.js","layer":"ssr"},"startTime":1750786510663,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11191,"timestamp":3307506163454,"id":426,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/styles.js","layer":"ssr"},"startTime":1750786510663,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11516,"timestamp":3307506163473,"id":427,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/call-stack-frame/call-stack-frame.js","layer":"ssr"},"startTime":1750786510663,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11626,"timestamp":3307506163513,"id":429,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/environment-name-label/environment-name-label.js","layer":"ssr"},"startTime":1750786510663,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":12307,"timestamp":3307506163434,"id":425,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/terminal.js","layer":"ssr"},"startTime":1750786510663,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":12607,"timestamp":3307506163492,"id":428,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/version-staleness-info/version-staleness-info.js","layer":"ssr"},"startTime":1750786510663,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":12960,"timestamp":3307506163533,"id":430,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-overlay-footer.js","layer":"ssr"},"startTime":1750786510663,"traceId":"027b520ae38c0c54"}]
[{"name":"build-module-js","duration":13416,"timestamp":3307506163551,"id":431,"parentId":405,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/hooks/use-delayed-render.js","layer":"ssr"},"startTime":1750786510663,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13567,"timestamp":3307506163568,"id":432,"parentId":406,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/gear-icon.js","layer":"ssr"},"startTime":1750786510663,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":18377,"timestamp":3307506178968,"id":434,"parentId":368,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/@swc/helpers/esm/_class_private_field_loose_key.js","layer":"ssr"},"startTime":1750786510679,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":18754,"timestamp":3307506178784,"id":433,"parentId":368,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/@swc/helpers/esm/_class_private_field_loose_base.js","layer":"ssr"},"startTime":1750786510679,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3331,"timestamp":3307506194802,"id":436,"parentId":419,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/error-source.js","layer":"ssr"},"startTime":1750786510695,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3472,"timestamp":3307506194885,"id":438,"parentId":413,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/component-stack-pseudo-html.js","layer":"ssr"},"startTime":1750786510695,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3795,"timestamp":3307506194949,"id":439,"parentId":417,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/eye-icon.js","layer":"ssr"},"startTime":1750786510695,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4434,"timestamp":3307506194858,"id":437,"parentId":419,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/stack-frame.js","layer":"ssr"},"startTime":1750786510695,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4428,"timestamp":3307506195039,"id":442,"parentId":417,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/system-icon.js","layer":"ssr"},"startTime":1750786510695,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4591,"timestamp":3307506195011,"id":441,"parentId":417,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/dark-icon.js","layer":"ssr"},"startTime":1750786510695,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4766,"timestamp":3307506194980,"id":440,"parentId":417,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/light-icon.js","layer":"ssr"},"startTime":1750786510695,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7254,"timestamp":3307506195061,"id":443,"parentId":418,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/use-minimum-loading-time-multiple.js","layer":"ssr"},"startTime":1750786510695,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7050,"timestamp":3307506197574,"id":444,"parentId":423,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/external.js","layer":"ssr"},"startTime":1750786510697,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7410,"timestamp":3307506197662,"id":447,"parentId":422,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/left-arrow.js","layer":"ssr"},"startTime":1750786510697,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7928,"timestamp":3307506197641,"id":446,"parentId":423,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/code-frame/parse-code-frame.js","layer":"ssr"},"startTime":1750786510697,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8366,"timestamp":3307506197617,"id":445,"parentId":423,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/file.js","layer":"ssr"},"startTime":1750786510697,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8448,"timestamp":3307506197682,"id":448,"parentId":422,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/right-arrow.js","layer":"ssr"},"startTime":1750786510697,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":17910,"timestamp":3307506190114,"id":435,"parentId":363,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/stacktrace-parser/stack-trace-parser.cjs.js","layer":"ssr"},"startTime":1750786510690,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7291,"timestamp":3307506203457,"id":450,"parentId":428,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/utils/cx.js","layer":"ssr"},"startTime":1750786510703,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7465,"timestamp":3307506203478,"id":451,"parentId":418,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/utils/merge-refs.js","layer":"ssr"},"startTime":1750786510703,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7844,"timestamp":3307506203415,"id":449,"parentId":423,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/utils/use-open-in-editor.js","layer":"ssr"},"startTime":1750786510703,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4569,"timestamp":3307506208357,"id":455,"parentId":421,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-type-label/error-type-label.js","layer":"ssr"},"startTime":1750786510708,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4848,"timestamp":3307506208311,"id":453,"parentId":421,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/error-overlay-toolbar.js","layer":"ssr"},"startTime":1750786510708,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5131,"timestamp":3307506208267,"id":452,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/index.js","layer":"ssr"},"startTime":1750786510708,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5306,"timestamp":3307506208336,"id":454,"parentId":421,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-message/error-message.js","layer":"ssr"},"startTime":1750786510708,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5351,"timestamp":3307506208432,"id":459,"parentId":421,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/body.js","layer":"ssr"},"startTime":1750786510708,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10783,"timestamp":3307506208396,"id":457,"parentId":421,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/dialog.js","layer":"ssr"},"startTime":1750786510708,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10939,"timestamp":3307506208415,"id":458,"parentId":421,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/header.js","layer":"ssr"},"startTime":1750786510708,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11400,"timestamp":3307506208376,"id":456,"parentId":421,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-nav/error-overlay-nav.js","layer":"ssr"},"startTime":1750786510708,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11497,"timestamp":3307506208466,"id":461,"parentId":421,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/overlay/overlay.js","layer":"ssr"},"startTime":1750786510708,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11847,"timestamp":3307506208449,"id":460,"parentId":421,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/call-stack/call-stack.js","layer":"ssr"},"startTime":1750786510708,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":12166,"timestamp":3307506208482,"id":462,"parentId":430,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-feedback/error-feedback.js","layer":"ssr"},"startTime":1750786510708,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4687,"timestamp":3307506221695,"id":465,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/index.js","layer":"ssr"},"startTime":1750786510721,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4891,"timestamp":3307506221652,"id":464,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/index.js","layer":"ssr"},"startTime":1750786510721,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5085,"timestamp":3307506221735,"id":467,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/fader/index.js","layer":"ssr"},"startTime":1750786510722,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5274,"timestamp":3307506221754,"id":468,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-bottom-stack/index.js","layer":"ssr"},"startTime":1750786510722,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5865,"timestamp":3307506221716,"id":466,"parentId":401,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/copy-button/index.js","layer":"ssr"},"startTime":1750786510722,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":19159,"timestamp":3307506214292,"id":463,"parentId":392,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js","layer":"ssr"},"startTime":1750786510714,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2446,"timestamp":3307506232443,"id":469,"parentId":437,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/webpack-module-path.js","layer":"ssr"},"startTime":1750786510732,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2836,"timestamp":3307506234306,"id":471,"parentId":453,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/copy-stack-trace-button.js","layer":"ssr"},"startTime":1750786510734,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3080,"timestamp":3307506234330,"id":472,"parentId":453,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/docs-link-button.js","layer":"ssr"},"startTime":1750786510734,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3463,"timestamp":3307506235747,"id":475,"parentId":413,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/index.js","layer":"ssr"},"startTime":1750786510736,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5517,"timestamp":3307506234248,"id":470,"parentId":453,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/nodejs-inspector-button.js","layer":"ssr"},"startTime":1750786510734,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4141,"timestamp":3307506235724,"id":474,"parentId":412,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/index.js","layer":"ssr"},"startTime":1750786510736,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4200,"timestamp":3307506235784,"id":477,"parentId":464,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-header.js","layer":"ssr"},"startTime":1750786510736,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4608,"timestamp":3307506235691,"id":473,"parentId":438,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/hydration-diff/diff-view.js","layer":"ssr"},"startTime":1750786510735,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4656,"timestamp":3307506235802,"id":478,"parentId":461,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/overlay.js","layer":"ssr"},"startTime":1750786510736,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5018,"timestamp":3307506235765,"id":476,"parentId":464,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog.js","layer":"ssr"},"startTime":1750786510736,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3209,"timestamp":3307506237689,"id":482,"parentId":464,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-content.js","layer":"ssr"},"startTime":1750786510737,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3356,"timestamp":3307506237646,"id":480,"parentId":464,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-body.js","layer":"ssr"},"startTime":1750786510737,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3512,"timestamp":3307506237606,"id":479,"parentId":465,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/styles.js","layer":"ssr"},"startTime":1750786510737,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10254,"timestamp":3307506237670,"id":481,"parentId":465,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/toast.js","layer":"ssr"},"startTime":1750786510737,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10396,"timestamp":3307506237708,"id":483,"parentId":464,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/styles.js","layer":"ssr"},"startTime":1750786510738,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6707,"timestamp":3307506241678,"id":485,"parentId":462,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/thumbs/thumbs-up.js","layer":"ssr"},"startTime":1750786510741,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6818,"timestamp":3307506241700,"id":486,"parentId":462,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/thumbs/thumbs-down.js","layer":"ssr"},"startTime":1750786510742,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7154,"timestamp":3307506241641,"id":484,"parentId":413,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/hot-linked-text/index.js","layer":"ssr"},"startTime":1750786510741,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":963,"timestamp":3307506252741,"id":487,"parentId":472,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/utils/parse-url-from-text.js","layer":"ssr"},"startTime":1750786510753,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1277,"timestamp":3307506253036,"id":488,"parentId":473,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/collapse-icon.js","layer":"ssr"},"startTime":1750786510753,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1694,"timestamp":3307506253101,"id":490,"parentId":476,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/hooks/use-measure-height.js","layer":"ssr"},"startTime":1750786510753,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1911,"timestamp":3307506253073,"id":489,"parentId":476,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/hooks/use-on-click-outside.js","layer":"ssr"},"startTime":1750786510753,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2795,"timestamp":3307506253129,"id":491,"parentId":478,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/body-locker.js","layer":"ssr"},"startTime":1750786510753,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2924,"timestamp":3307506253332,"id":492,"parentId":484,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/magic-identifier.js","layer":"ssr"},"startTime":1750786510753,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1429,"timestamp":3307506256583,"id":493,"parentId":425,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/anser/index.js","layer":"ssr"},"startTime":1750786510756,"traceId":"027b520ae38c0c54"},{"name":"make","duration":614322,"timestamp":3307505643850,"id":120,"parentId":119,"tags":{},"startTime":1750786510144,"traceId":"027b520ae38c0c54"},{"name":"chunk-graph","duration":4325,"timestamp":3307506265231,"id":495,"parentId":494,"tags":{},"startTime":1750786510765,"traceId":"027b520ae38c0c54"},{"name":"optimize-modules","duration":13,"timestamp":3307506269616,"id":497,"parentId":494,"tags":{},"startTime":1750786510769,"traceId":"027b520ae38c0c54"},{"name":"optimize-chunks","duration":3480,"timestamp":3307506269643,"id":498,"parentId":494,"tags":{},"startTime":1750786510769,"traceId":"027b520ae38c0c54"},{"name":"optimize-tree","duration":6,"timestamp":3307506273140,"id":499,"parentId":494,"tags":{},"startTime":1750786510773,"traceId":"027b520ae38c0c54"},{"name":"optimize-chunk-modules","duration":4,"timestamp":3307506273183,"id":500,"parentId":494,"tags":{},"startTime":1750786510773,"traceId":"027b520ae38c0c54"},{"name":"optimize","duration":4451,"timestamp":3307506269607,"id":496,"parentId":494,"tags":{},"startTime":1750786510769,"traceId":"027b520ae38c0c54"},{"name":"module-hash","duration":5076,"timestamp":3307506276121,"id":501,"parentId":494,"tags":{},"startTime":1750786510776,"traceId":"027b520ae38c0c54"},{"name":"code-generation","duration":11994,"timestamp":3307506281213,"id":502,"parentId":494,"tags":{},"startTime":1750786510781,"traceId":"027b520ae38c0c54"},{"name":"hash","duration":3118,"timestamp":3307506296060,"id":503,"parentId":494,"tags":{},"startTime":1750786510796,"traceId":"027b520ae38c0c54"},{"name":"code-generation-jobs","duration":202,"timestamp":3307506299178,"id":504,"parentId":494,"tags":{},"startTime":1750786510799,"traceId":"027b520ae38c0c54"},{"name":"module-assets","duration":114,"timestamp":3307506299373,"id":505,"parentId":494,"tags":{},"startTime":1750786510799,"traceId":"027b520ae38c0c54"},{"name":"create-chunk-assets","duration":55825,"timestamp":3307506299491,"id":506,"parentId":494,"tags":{},"startTime":1750786510799,"traceId":"027b520ae38c0c54"},{"name":"seal","duration":93263,"timestamp":3307506264146,"id":494,"parentId":119,"tags":{},"startTime":1750786510764,"traceId":"027b520ae38c0c54"},{"name":"webpack-compilation","duration":715799,"timestamp":3307505643319,"id":119,"parentId":117,"tags":{"name":"server"},"startTime":1750786510143,"traceId":"027b520ae38c0c54"},{"name":"emit","duration":20454,"timestamp":3307506359192,"id":507,"parentId":117,"tags":{},"startTime":1750786510859,"traceId":"027b520ae38c0c54"},{"name":"webpack-invalidated-server","duration":740093,"timestamp":3307505640647,"id":117,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750786510140,"traceId":"027b520ae38c0c54"},{"name":"build-module","duration":782,"timestamp":3307506401607,"id":515,"parentId":512,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!","layer":"app-pages-browser"},"startTime":1750786510901,"traceId":"027b520ae38c0c54"},{"name":"build-module","duration":84,"timestamp":3307506402435,"id":516,"parentId":513,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=false!","layer":"app-pages-browser"},"startTime":1750786510902,"traceId":"027b520ae38c0c54"},{"name":"build-module","duration":298,"timestamp":3307506402526,"id":517,"parentId":514,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!","layer":"app-pages-browser"},"startTime":1750786510902,"traceId":"027b520ae38c0c54"},{"name":"add-entry","duration":16679,"timestamp":3307506386262,"id":513,"parentId":509,"tags":{"request":"next-flight-client-entry-loader?server=false!"},"startTime":1750786510886,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5182,"timestamp":3307506408387,"id":520,"parentId":517,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/client-segment.js","layer":"app-pages-browser"},"startTime":1750786510908,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7875,"timestamp":3307506408335,"id":519,"parentId":517,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/client-page.js","layer":"app-pages-browser"},"startTime":1750786510908,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8253,"timestamp":3307506408484,"id":523,"parentId":517,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/render-from-template-context.js","layer":"app-pages-browser"},"startTime":1750786510908,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10435,"timestamp":3307506406764,"id":518,"parentId":511,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/app-next-dev.js","layer":"app-pages-browser"},"startTime":1750786510907,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9316,"timestamp":3307506408548,"id":525,"parentId":517,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/metadata/async-metadata.js","layer":"app-pages-browser"},"startTime":1750786510908,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9778,"timestamp":3307506408577,"id":526,"parentId":517,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/metadata/metadata-boundary.js","layer":"app-pages-browser"},"startTime":1750786510908,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11896,"timestamp":3307506408517,"id":524,"parentId":517,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","layer":"app-pages-browser"},"startTime":1750786510908,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13283,"timestamp":3307506408424,"id":521,"parentId":517,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/error-boundary.js","layer":"app-pages-browser"},"startTime":1750786510908,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":15814,"timestamp":3307506408456,"id":522,"parentId":517,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/layout-router.js","layer":"app-pages-browser"},"startTime":1750786510908,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1385,"timestamp":3307506441102,"id":537,"parentId":526,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/metadata/metadata-constants.js","layer":"app-pages-browser"},"startTime":1750786510941,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7335,"timestamp":3307506439163,"id":527,"parentId":520,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/invariant-error.js","layer":"app-pages-browser"},"startTime":1750786510939,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5950,"timestamp":3307506440934,"id":531,"parentId":521,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/is-next-router-error.js","layer":"app-pages-browser"},"startTime":1750786510941,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6320,"timestamp":3307506440896,"id":530,"parentId":521,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/navigation-untracked.js","layer":"app-pages-browser"},"startTime":1750786510941,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6700,"timestamp":3307506440820,"id":529,"parentId":523,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js","layer":"app-pages-browser"},"startTime":1750786510941,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6956,"timestamp":3307506440963,"id":532,"parentId":521,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/nav-failure-handler.js","layer":"app-pages-browser"},"startTime":1750786510941,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7184,"timestamp":3307506440992,"id":533,"parentId":522,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/unresolved-thenable.js","layer":"app-pages-browser"},"startTime":1750786510941,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7444,"timestamp":3307506441023,"id":534,"parentId":522,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/match-segments.js","layer":"app-pages-browser"},"startTime":1750786510941,"traceId":"027b520ae38c0c54"}]
[{"name":"build-module-js","duration":7979,"timestamp":3307506441075,"id":536,"parentId":522,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/use-action-queue.js","layer":"app-pages-browser"},"startTime":1750786510941,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8467,"timestamp":3307506441049,"id":535,"parentId":522,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/redirect-boundary.js","layer":"app-pages-browser"},"startTime":1750786510941,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8756,"timestamp":3307506441159,"id":538,"parentId":522,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/router-reducer-types.js","layer":"app-pages-browser"},"startTime":1750786510941,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9030,"timestamp":3307506441209,"id":540,"parentId":522,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/create-router-cache-key.js","layer":"app-pages-browser"},"startTime":1750786510941,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9399,"timestamp":3307506441234,"id":541,"parentId":524,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js","layer":"app-pages-browser"},"startTime":1750786510941,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9593,"timestamp":3307506441259,"id":542,"parentId":524,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/utils/warn-once.js","layer":"app-pages-browser"},"startTime":1750786510941,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9797,"timestamp":3307506441284,"id":543,"parentId":522,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js","layer":"app-pages-browser"},"startTime":1750786510941,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10036,"timestamp":3307506441306,"id":544,"parentId":522,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js","layer":"app-pages-browser"},"startTime":1750786510941,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":14189,"timestamp":3307506441185,"id":539,"parentId":522,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/fetch-server-response.js","layer":"app-pages-browser"},"startTime":1750786510941,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2555,"timestamp":3307506456520,"id":545,"parentId":523,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/@swc/helpers/esm/_interop_require_wildcard.js","layer":"app-pages-browser"},"startTime":1750786510956,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2319,"timestamp":3307506456868,"id":546,"parentId":521,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/@swc/helpers/esm/_interop_require_default.js","layer":"app-pages-browser"},"startTime":1750786510957,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":20407,"timestamp":3307506439280,"id":528,"parentId":510,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/@next/react-refresh-utils/dist/runtime.js","layer":"app-pages-browser"},"startTime":1750786510939,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3012,"timestamp":3307506457040,"id":551,"parentId":518,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/dev/dev-build-indicator/initialize-for-app-router.js","layer":"app-pages-browser"},"startTime":1750786510957,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3507,"timestamp":3307506456950,"id":548,"parentId":518,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/app-bootstrap.js","layer":"app-pages-browser"},"startTime":1750786510957,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3978,"timestamp":3307506456923,"id":547,"parentId":518,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/app-webpack.js","layer":"app-pages-browser"},"startTime":1750786510957,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7238,"timestamp":3307506457019,"id":550,"parentId":518,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/app-index.js","layer":"app-pages-browser"},"startTime":1750786510957,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7909,"timestamp":3307506457072,"id":552,"parentId":525,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/metadata/browser-resolved-metadata.js","layer":"app-pages-browser"},"startTime":1750786510957,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7267,"timestamp":3307506461276,"id":555,"parentId":536,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/is-thenable.js","layer":"app-pages-browser"},"startTime":1750786510961,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7591,"timestamp":3307506461249,"id":554,"parentId":530,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js","layer":"app-pages-browser"},"startTime":1750786510961,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8039,"timestamp":3307506461194,"id":553,"parentId":531,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/redirect-error.js","layer":"app-pages-browser"},"startTime":1750786510961,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8350,"timestamp":3307506461322,"id":557,"parentId":535,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/redirect.js","layer":"app-pages-browser"},"startTime":1750786510961,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8600,"timestamp":3307506461344,"id":558,"parentId":540,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/segment.js","layer":"app-pages-browser"},"startTime":1750786510961,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8819,"timestamp":3307506461389,"id":560,"parentId":536,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator.js","layer":"app-pages-browser"},"startTime":1750786510961,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9061,"timestamp":3307506461368,"id":559,"parentId":532,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/create-href-from-url.js","layer":"app-pages-browser"},"startTime":1750786510961,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9205,"timestamp":3307506461453,"id":563,"parentId":519,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/request/search-params.browser.js","layer":"app-pages-browser"},"startTime":1750786510961,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10313,"timestamp":3307506461300,"id":556,"parentId":535,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/navigation.js","layer":"app-pages-browser"},"startTime":1750786510961,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10457,"timestamp":3307506461432,"id":562,"parentId":520,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/request/params.browser.js","layer":"app-pages-browser"},"startTime":1750786510961,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10960,"timestamp":3307506461411,"id":561,"parentId":544,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/interception-routes.js","layer":"app-pages-browser"},"startTime":1750786510961,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9341,"timestamp":3307506465855,"id":567,"parentId":539,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/app-build-id.js","layer":"app-pages-browser"},"startTime":1750786510966,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10062,"timestamp":3307506465804,"id":565,"parentId":539,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/app-find-source-map-url.js","layer":"app-pages-browser"},"startTime":1750786510966,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10492,"timestamp":3307506465738,"id":564,"parentId":539,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/app-call-server.js","layer":"app-pages-browser"},"startTime":1750786510966,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10736,"timestamp":3307506465901,"id":569,"parentId":539,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/set-cache-busting-search-param.js","layer":"app-pages-browser"},"startTime":1750786510966,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11460,"timestamp":3307506465878,"id":568,"parentId":539,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/app-router-headers.js","layer":"app-pages-browser"},"startTime":1750786510966,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11888,"timestamp":3307506465831,"id":566,"parentId":539,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/flight-data-helpers.js","layer":"app-pages-browser"},"startTime":1750786510966,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":21153,"timestamp":3307506465924,"id":570,"parentId":539,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js","layer":"app-pages-browser"},"startTime":1750786510966,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":30692,"timestamp":3307506456972,"id":549,"parentId":518,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/require-instrumentation-client.js","layer":"app-pages-browser"},"startTime":1750786510957,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7892,"timestamp":3307506480287,"id":571,"parentId":551,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js","layer":"app-pages-browser"},"startTime":1750786510980,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":21322,"timestamp":3307506498639,"id":580,"parentId":570,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/dev/hot-reloader-types.js","layer":"app-pages-browser"},"startTime":1750786510998,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":23656,"timestamp":3307506496747,"id":573,"parentId":553,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/redirect-status-code.js","layer":"app-pages-browser"},"startTime":1750786510997,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":23826,"timestamp":3307506496930,"id":576,"parentId":569,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/hash.js","layer":"app-pages-browser"},"startTime":1750786510997,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":24061,"timestamp":3307506496954,"id":577,"parentId":556,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/reducers/get-segment-value.js","layer":"app-pages-browser"},"startTime":1750786510997,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":24405,"timestamp":3307506496901,"id":575,"parentId":556,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js","layer":"app-pages-browser"},"startTime":1750786510997,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":25867,"timestamp":3307506497005,"id":579,"parentId":560,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/dev-render-indicator.js","layer":"app-pages-browser"},"startTime":1750786510997,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":26558,"timestamp":3307506496859,"id":574,"parentId":556,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/navigation.react-server.js","layer":"app-pages-browser"},"startTime":1750786510997,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":26802,"timestamp":3307506496979,"id":578,"parentId":561,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/app-paths.js","layer":"app-pages-browser"},"startTime":1750786510997,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":28032,"timestamp":3307506498835,"id":584,"parentId":570,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/errors/stitched-error.js","layer":"app-pages-browser"},"startTime":1750786510999,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":29844,"timestamp":3307506498811,"id":583,"parentId":570,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/errors/runtime-error-handler.js","layer":"app-pages-browser"},"startTime":1750786510999,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":31118,"timestamp":3307506498879,"id":586,"parentId":570,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/parse-stack.js","layer":"app-pages-browser"},"startTime":1750786510999,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":32044,"timestamp":3307506498857,"id":585,"parentId":570,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/format-webpack-messages.js","layer":"app-pages-browser"},"startTime":1750786510999,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":32461,"timestamp":3307506498902,"id":587,"parentId":570,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js","layer":"app-pages-browser"},"startTime":1750786510999,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":32971,"timestamp":3307506498925,"id":588,"parentId":570,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/use-websocket.js","layer":"app-pages-browser"},"startTime":1750786510999,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":33411,"timestamp":3307506498947,"id":589,"parentId":570,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/parse-component-stack.js","layer":"app-pages-browser"},"startTime":1750786510999,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":34144,"timestamp":3307506498786,"id":582,"parentId":570,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/errors/use-error-handler.js","layer":"app-pages-browser"},"startTime":1750786510999,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":34255,"timestamp":3307506498969,"id":590,"parentId":570,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.js","layer":"app-pages-browser"},"startTime":1750786510999,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":34496,"timestamp":3307506498991,"id":591,"parentId":570,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/report-hmr-latency.js","layer":"app-pages-browser"},"startTime":1750786510999,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":29725,"timestamp":3307506504016,"id":595,"parentId":570,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/strip-ansi/index.js","layer":"app-pages-browser"},"startTime":1750786511004,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":35022,"timestamp":3307506499033,"id":593,"parentId":571,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js","layer":"app-pages-browser"},"startTime":1750786510999,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":30130,"timestamp":3307506504112,"id":597,"parentId":547,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/encode-uri-path.js","layer":"app-pages-browser"},"startTime":1750786511004,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":37331,"timestamp":3307506498751,"id":581,"parentId":570,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/shared.js","layer":"app-pages-browser"},"startTime":1750786510999,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":32400,"timestamp":3307506504134,"id":598,"parentId":550,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/app-link-gc.js","layer":"app-pages-browser"},"startTime":1750786511004,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":38126,"timestamp":3307506499012,"id":592,"parentId":570,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/turbopack-hot-reloader-common.js","layer":"app-pages-browser"},"startTime":1750786510999,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":33184,"timestamp":3307506504154,"id":599,"parentId":550,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js","layer":"app-pages-browser"},"startTime":1750786511004,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":33427,"timestamp":3307506504242,"id":603,"parentId":550,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/app/client-entry.js","layer":"app-pages-browser"},"startTime":1750786511004,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":34369,"timestamp":3307506504222,"id":602,"parentId":550,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js","layer":"app-pages-browser"},"startTime":1750786511004,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":35374,"timestamp":3307506504174,"id":600,"parentId":550,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/app-router-instance.js","layer":"app-pages-browser"},"startTime":1750786511004,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":27747,"timestamp":3307506524635,"id":605,"parentId":563,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/request/search-params.browser.dev.js","layer":"app-pages-browser"},"startTime":1750786511024,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":28195,"timestamp":3307506524715,"id":606,"parentId":562,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/request/params.browser.dev.js","layer":"app-pages-browser"},"startTime":1750786511025,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":50334,"timestamp":3307506504201,"id":601,"parentId":550,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/app-router.js","layer":"app-pages-browser"},"startTime":1750786511004,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":54902,"timestamp":3307506504056,"id":596,"parentId":547,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/deployment-id.js","layer":"app-pages-browser"},"startTime":1750786511004,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2534,"timestamp":3307506556818,"id":610,"parentId":550,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/globals/patch-console.js","layer":"app-pages-browser"},"startTime":1750786511057,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2783,"timestamp":3307506556788,"id":609,"parentId":550,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/globals/handle-global-errors.js","layer":"app-pages-browser"},"startTime":1750786511057,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3335,"timestamp":3307506559859,"id":618,"parentId":584,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/is-error.js","layer":"app-pages-browser"},"startTime":1750786511060,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3483,"timestamp":3307506559997,"id":619,"parentId":584,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/lib/error-telemetry-utils.js","layer":"app-pages-browser"},"startTime":1750786511060,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3821,"timestamp":3307506560386,"id":632,"parentId":586,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/stacktrace-parser/stack-trace-parser.cjs.js","layer":"app-pages-browser"},"startTime":1750786511060,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3653,"timestamp":3307506560730,"id":633,"parentId":592,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/@swc/helpers/esm/_class_private_field_loose_base.js","layer":"app-pages-browser"},"startTime":1750786511061,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3668,"timestamp":3307506560793,"id":634,"parentId":592,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/@swc/helpers/esm/_class_private_field_loose_key.js","layer":"app-pages-browser"},"startTime":1750786511061,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7933,"timestamp":3307506556885,"id":613,"parentId":574,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/not-found.js","layer":"app-pages-browser"},"startTime":1750786511057,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8435,"timestamp":3307506556841,"id":611,"parentId":550,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js","layer":"app-pages-browser"},"startTime":1750786511057,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9051,"timestamp":3307506556863,"id":612,"parentId":550,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js","layer":"app-pages-browser"},"startTime":1750786511057,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10490,"timestamp":3307506556907,"id":614,"parentId":574,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/forbidden.js","layer":"app-pages-browser"},"startTime":1750786511057,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8606,"timestamp":3307506559831,"id":617,"parentId":578,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/page-path/ensure-leading-slash.js","layer":"app-pages-browser"},"startTime":1750786511060,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9847,"timestamp":3307506559790,"id":616,"parentId":574,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/unstable-rethrow.js","layer":"app-pages-browser"},"startTime":1750786511060,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10232,"timestamp":3307506559723,"id":615,"parentId":574,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/unauthorized.js","layer":"app-pages-browser"},"startTime":1750786511060,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10190,"timestamp":3307506560112,"id":622,"parentId":582,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/errors/attach-hydration-error-state.js","layer":"app-pages-browser"},"startTime":1750786511060,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10775,"timestamp":3307506560065,"id":620,"parentId":582,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/lib/console.js","layer":"app-pages-browser"},"startTime":1750786511060,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11335,"timestamp":3307506560133,"id":623,"parentId":582,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/errors/hydration-error-info.js","layer":"app-pages-browser"},"startTime":1750786511060,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":12936,"timestamp":3307506560090,"id":621,"parentId":586,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/is-hydration-error.js","layer":"app-pages-browser"},"startTime":1750786511060,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13163,"timestamp":3307506560200,"id":626,"parentId":587,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/errors/constants.js","layer":"app-pages-browser"},"startTime":1750786511060,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13493,"timestamp":3307506560178,"id":625,"parentId":582,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/errors/enqueue-client-error.js","layer":"app-pages-browser"},"startTime":1750786511060,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13827,"timestamp":3307506560156,"id":624,"parentId":582,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/errors/console-error.js","layer":"app-pages-browser"},"startTime":1750786511060,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":14088,"timestamp":3307506560265,"id":629,"parentId":587,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/dev-overlay.js","layer":"app-pages-browser"},"startTime":1750786511060,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":19916,"timestamp":3307506560244,"id":628,"parentId":587,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js","layer":"app-pages-browser"},"startTime":1750786511060,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":20357,"timestamp":3307506560222,"id":627,"parentId":587,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js","layer":"app-pages-browser"},"startTime":1750786511060,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":20619,"timestamp":3307506560296,"id":630,"parentId":588,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/get-socket-url.js","layer":"app-pages-browser"},"startTime":1750786511060,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":20786,"timestamp":3307506560318,"id":631,"parentId":588,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/dev/noop-turbopack-hmr.js","layer":"app-pages-browser"},"startTime":1750786511060,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2357,"timestamp":3307506588070,"id":636,"parentId":605,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/server/web/spec-extension/adapters/reflect.js","layer":"app-pages-browser"},"startTime":1750786511088,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":34815,"timestamp":3307506556609,"id":607,"parentId":528,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/@next/react-refresh-utils/dist/internal/helpers.js","layer":"app-pages-browser"},"startTime":1750786511056,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":35025,"timestamp":3307506556728,"id":608,"parentId":550,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/polyfills/polyfill-module.js","layer":"app-pages-browser"},"startTime":1750786511057,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4129,"timestamp":3307506588002,"id":635,"parentId":605,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/utils/reflect-utils.js","layer":"app-pages-browser"},"startTime":1750786511088,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4038,"timestamp":3307506588427,"id":638,"parentId":600,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/add-base-path.js","layer":"app-pages-browser"},"startTime":1750786511088,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4565,"timestamp":3307506588467,"id":639,"parentId":600,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/segment-cache.js","layer":"app-pages-browser"},"startTime":1750786511088,"traceId":"027b520ae38c0c54"}]
[{"name":"build-module-js","duration":5042,"timestamp":3307506588511,"id":641,"parentId":600,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/router-reducer.js","layer":"app-pages-browser"},"startTime":1750786511088,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8609,"timestamp":3307506588532,"id":642,"parentId":602,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.js","layer":"app-pages-browser"},"startTime":1750786511088,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9551,"timestamp":3307506588490,"id":640,"parentId":600,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/links.js","layer":"app-pages-browser"},"startTime":1750786511088,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10003,"timestamp":3307506588552,"id":643,"parentId":602,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/compute-changed-path.js","layer":"app-pages-browser"},"startTime":1750786511088,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":16920,"timestamp":3307506588611,"id":646,"parentId":600,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js","layer":"app-pages-browser"},"startTime":1750786511088,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":17450,"timestamp":3307506588592,"id":645,"parentId":602,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js","layer":"app-pages-browser"},"startTime":1750786511088,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":18408,"timestamp":3307506588573,"id":644,"parentId":602,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js","layer":"app-pages-browser"},"startTime":1750786511088,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13138,"timestamp":3307506594083,"id":647,"parentId":618,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/is-plain-object.js","layer":"app-pages-browser"},"startTime":1750786511094,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13333,"timestamp":3307506594132,"id":648,"parentId":611,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js","layer":"app-pages-browser"},"startTime":1750786511094,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13627,"timestamp":3307506594159,"id":649,"parentId":629,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/shadow-portal.js","layer":"app-pages-browser"},"startTime":1750786511094,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13828,"timestamp":3307506594254,"id":653,"parentId":629,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/styles/colors.js","layer":"app-pages-browser"},"startTime":1750786511094,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":14178,"timestamp":3307506594226,"id":652,"parentId":629,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/styles/css-reset.js","layer":"app-pages-browser"},"startTime":1750786511094,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":14552,"timestamp":3307506594183,"id":650,"parentId":629,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/styles/base.js","layer":"app-pages-browser"},"startTime":1750786511094,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":14972,"timestamp":3307506594205,"id":651,"parentId":629,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/styles/component-styles.js","layer":"app-pages-browser"},"startTime":1750786511094,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10353,"timestamp":3307506611959,"id":662,"parentId":628,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js","layer":"app-pages-browser"},"startTime":1750786511112,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":28581,"timestamp":3307506594275,"id":654,"parentId":629,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/styles/dark-theme.js","layer":"app-pages-browser"},"startTime":1750786511094,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":29087,"timestamp":3307506594315,"id":656,"parentId":629,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay/error-overlay.js","layer":"app-pages-browser"},"startTime":1750786511094,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":29551,"timestamp":3307506594295,"id":655,"parentId":629,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/render-error.js","layer":"app-pages-browser"},"startTime":1750786511094,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":30534,"timestamp":3307506594335,"id":657,"parentId":629,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-indicator.js","layer":"app-pages-browser"},"startTime":1750786511094,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13634,"timestamp":3307506611746,"id":659,"parentId":630,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/normalized-asset-prefix.js","layer":"app-pages-browser"},"startTime":1750786511112,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13821,"timestamp":3307506611925,"id":661,"parentId":629,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/preferences.js","layer":"app-pages-browser"},"startTime":1750786511112,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13958,"timestamp":3307506612041,"id":663,"parentId":601,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/remove-base-path.js","layer":"app-pages-browser"},"startTime":1750786511112,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":14425,"timestamp":3307506611859,"id":660,"parentId":628,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/css.js","layer":"app-pages-browser"},"startTime":1750786511112,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":14413,"timestamp":3307506612104,"id":664,"parentId":601,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/has-base-path.js","layer":"app-pages-browser"},"startTime":1750786511112,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":14613,"timestamp":3307506612183,"id":666,"parentId":601,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/is-bot.js","layer":"app-pages-browser"},"startTime":1750786511112,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":15006,"timestamp":3307506612155,"id":665,"parentId":601,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js","layer":"app-pages-browser"},"startTime":1750786511112,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":15238,"timestamp":3307506612265,"id":667,"parentId":601,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js","layer":"app-pages-browser"},"startTime":1750786511112,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":15623,"timestamp":3307506612314,"id":668,"parentId":601,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/app-router-announcer.js","layer":"app-pages-browser"},"startTime":1750786511112,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":875452,"timestamp":3307506588125,"id":637,"parentId":539,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/react-server-dom-webpack/client.js","layer":"app-pages-browser"},"startTime":1750786511088,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":832883,"timestamp":3307506631754,"id":669,"parentId":638,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/add-path-prefix.js","layer":"app-pages-browser"},"startTime":1750786511132,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":833576,"timestamp":3307506631886,"id":672,"parentId":611,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/react-client-callbacks/report-global-error.js","layer":"app-pages-browser"},"startTime":1750786511132,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":834532,"timestamp":3307506631917,"id":673,"parentId":616,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/unstable-rethrow.browser.js","layer":"app-pages-browser"},"startTime":1750786511132,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":835600,"timestamp":3307506631830,"id":670,"parentId":610,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/globals/intercept-console-error.js","layer":"app-pages-browser"},"startTime":1750786511132,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":880312,"timestamp":3307506594375,"id":658,"parentId":520,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/react/jsx-runtime.js","layer":"app-pages-browser"},"startTime":1750786511094,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6991,"timestamp":3307507468933,"id":678,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/styles.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8401,"timestamp":3307507468777,"id":675,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/container/build-error.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":13032,"timestamp":3307507468982,"id":679,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/terminal.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":14230,"timestamp":3307507468904,"id":677,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/code-frame/code-frame.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":15565,"timestamp":3307507468872,"id":676,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/container/errors.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":16275,"timestamp":3307507469020,"id":680,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/version-staleness-info/version-staleness-info.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":17055,"timestamp":3307507469047,"id":681,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/call-stack-frame/call-stack-frame.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":17394,"timestamp":3307507469185,"id":685,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-overlay-footer.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":36719,"timestamp":3307507469069,"id":682,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/editor-link.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":37756,"timestamp":3307507469159,"id":684,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-pagination/error-overlay-pagination.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":38939,"timestamp":3307507469120,"id":683,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-layout/error-overlay-layout.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":39650,"timestamp":3307507469235,"id":687,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":40088,"timestamp":3307507469212,"id":686,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/environment-name-label/environment-name-label.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":40505,"timestamp":3307507469394,"id":691,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/index.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":41494,"timestamp":3307507469257,"id":688,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/turbopack-info.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":42780,"timestamp":3307507469291,"id":689,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/route-info.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":42988,"timestamp":3307507469421,"id":692,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/index.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":43299,"timestamp":3307507469504,"id":695,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/fader/index.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":43640,"timestamp":3307507469526,"id":696,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-bottom-stack/index.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":45612,"timestamp":3307507469452,"id":693,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/index.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":46795,"timestamp":3307507469351,"id":690,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":47493,"timestamp":3307507469475,"id":694,"parentId":651,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/copy-button/index.js","layer":"app-pages-browser"},"startTime":1750786511969,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":28267,"timestamp":3307507488928,"id":697,"parentId":664,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/path-has-prefix.js","layer":"app-pages-browser"},"startTime":1750786511989,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":28488,"timestamp":3307507489064,"id":699,"parentId":656,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/hooks/use-delayed-render.js","layer":"app-pages-browser"},"startTime":1750786511989,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":28980,"timestamp":3307507489023,"id":698,"parentId":655,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/get-error-by-type.js","layer":"app-pages-browser"},"startTime":1750786511989,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":29242,"timestamp":3307507489097,"id":700,"parentId":657,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/gear-icon.js","layer":"app-pages-browser"},"startTime":1750786511989,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":30686,"timestamp":3307507489152,"id":702,"parentId":657,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js","layer":"app-pages-browser"},"startTime":1750786511989,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":31877,"timestamp":3307507489125,"id":701,"parentId":657,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/next-logo.js","layer":"app-pages-browser"},"startTime":1750786511989,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":909090,"timestamp":3307506631857,"id":671,"parentId":523,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/react/index.js","layer":"app-pages-browser"},"startTime":1750786511132,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":15367,"timestamp":3307507525975,"id":703,"parentId":669,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/parse-path.js","layer":"app-pages-browser"},"startTime":1750786512026,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":15716,"timestamp":3307507526055,"id":704,"parentId":638,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/normalize-trailing-slash.js","layer":"app-pages-browser"},"startTime":1750786512026,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":16082,"timestamp":3307507526103,"id":706,"parentId":641,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/reducers/server-patch-reducer.js","layer":"app-pages-browser"},"startTime":1750786512026,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":16420,"timestamp":3307507526124,"id":707,"parentId":641,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/reducers/restore-reducer.js","layer":"app-pages-browser"},"startTime":1750786512026,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":16980,"timestamp":3307507526143,"id":708,"parentId":641,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/reducers/refresh-reducer.js","layer":"app-pages-browser"},"startTime":1750786512026,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":17333,"timestamp":3307507526202,"id":711,"parentId":645,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/apply-flight-data.js","layer":"app-pages-browser"},"startTime":1750786512026,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":17901,"timestamp":3307507526162,"id":709,"parentId":641,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js","layer":"app-pages-browser"},"startTime":1750786512026,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":19352,"timestamp":3307507526082,"id":705,"parentId":641,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js","layer":"app-pages-browser"},"startTime":1750786512026,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":19887,"timestamp":3307507526220,"id":712,"parentId":646,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/promise-queue.js","layer":"app-pages-browser"},"startTime":1750786512026,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":21851,"timestamp":3307507526183,"id":710,"parentId":641,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/reducers/server-action-reducer.js","layer":"app-pages-browser"},"startTime":1750786512026,"traceId":"027b520ae38c0c54"},{"name":"postcss-process","duration":122115,"timestamp":3307507449794,"id":674,"parentId":604,"tags":{},"startTime":1750786511950,"traceId":"027b520ae38c0c54"},{"name":"postcss-loader","duration":1067659,"timestamp":3307506504351,"id":604,"parentId":594,"tags":{},"startTime":1750786511004,"traceId":"027b520ae38c0c54"},{"name":"css-loader","duration":17729,"timestamp":3307507572115,"id":713,"parentId":594,"tags":{"astUsed":"true"},"startTime":1750786512072,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":5176,"timestamp":3307507592210,"id":726,"parentId":679,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/anser/index.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6565,"timestamp":3307507591903,"id":714,"parentId":676,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/error-source.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6890,"timestamp":3307507591997,"id":716,"parentId":679,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/external.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7256,"timestamp":3307507592020,"id":717,"parentId":679,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/utils/use-open-in-editor.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7496,"timestamp":3307507592061,"id":719,"parentId":680,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/utils/cx.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":7780,"timestamp":3307507592080,"id":720,"parentId":676,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/component-stack-pseudo-html.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8561,"timestamp":3307507591970,"id":715,"parentId":679,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/stack-frame.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8680,"timestamp":3307507592146,"id":723,"parentId":675,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/index.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9399,"timestamp":3307507592040,"id":718,"parentId":679,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/file.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9511,"timestamp":3307507592165,"id":724,"parentId":676,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/index.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10152,"timestamp":3307507592119,"id":722,"parentId":685,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-feedback/error-feedback.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10691,"timestamp":3307507592099,"id":721,"parentId":677,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/code-frame/parse-code-frame.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10741,"timestamp":3307507592320,"id":730,"parentId":684,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/left-arrow.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11014,"timestamp":3307507592295,"id":729,"parentId":666,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/html-bots.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11499,"timestamp":3307507592190,"id":725,"parentId":676,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/hot-linked-text/index.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11599,"timestamp":3307507592359,"id":732,"parentId":701,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/utils/merge-refs.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11893,"timestamp":3307507592340,"id":731,"parentId":684,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/right-arrow.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":12076,"timestamp":3307507592394,"id":734,"parentId":691,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-body.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":12290,"timestamp":3307507592412,"id":735,"parentId":691,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-content.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":12495,"timestamp":3307507592431,"id":736,"parentId":691,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-header.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":12701,"timestamp":3307507592457,"id":737,"parentId":691,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/styles.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":12979,"timestamp":3307507592479,"id":738,"parentId":692,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/styles.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":14948,"timestamp":3307507592497,"id":739,"parentId":692,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/toast.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":15326,"timestamp":3307507592515,"id":740,"parentId":690,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/eye-icon.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":15620,"timestamp":3307507592532,"id":741,"parentId":690,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/light-icon.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"}]
[{"name":"build-module-js","duration":16124,"timestamp":3307507592551,"id":742,"parentId":690,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/dark-icon.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":16981,"timestamp":3307507592377,"id":733,"parentId":691,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":17152,"timestamp":3307507592569,"id":743,"parentId":690,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/system-icon.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":17692,"timestamp":3307507592607,"id":745,"parentId":683,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/error-overlay-toolbar.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":31772,"timestamp":3307507592588,"id":744,"parentId":693,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/call-stack/call-stack.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":32020,"timestamp":3307507592681,"id":749,"parentId":683,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/dialog.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":32312,"timestamp":3307507592644,"id":747,"parentId":683,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-type-label/error-type-label.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":32507,"timestamp":3307507592699,"id":750,"parentId":683,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/header.js","layer":"app-pages-browser"},"startTime":1750786512093,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":32716,"timestamp":3307507592723,"id":751,"parentId":683,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/body.js","layer":"app-pages-browser"},"startTime":1750786512093,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":32932,"timestamp":3307507592755,"id":752,"parentId":683,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/overlay/overlay.js","layer":"app-pages-browser"},"startTime":1750786512093,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":34728,"timestamp":3307507592663,"id":748,"parentId":683,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-nav/error-overlay-nav.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":35141,"timestamp":3307507592625,"id":746,"parentId":683,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-message/error-message.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":35335,"timestamp":3307507592772,"id":753,"parentId":701,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/use-minimum-loading-time-multiple.js","layer":"app-pages-browser"},"startTime":1750786512093,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":14802,"timestamp":3307507613603,"id":755,"parentId":704,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/router/utils/remove-trailing-slash.js","layer":"app-pages-browser"},"startTime":1750786512113,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":43949,"timestamp":3307507592283,"id":728,"parentId":550,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/react-dom/client.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":44090,"timestamp":3307507592262,"id":727,"parentId":522,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/react-dom/index.js","layer":"app-pages-browser"},"startTime":1750786512092,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":43680,"timestamp":3307507592790,"id":754,"parentId":528,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/react-refresh/runtime.js","layer":"app-pages-browser"},"startTime":1750786512093,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2703,"timestamp":3307507634689,"id":760,"parentId":715,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/utils/webpack-module-path.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3385,"timestamp":3307507634617,"id":759,"parentId":725,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/magic-identifier.js","layer":"app-pages-browser"},"startTime":1750786512134,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":3625,"timestamp":3307507634788,"id":764,"parentId":724,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/overlay.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4077,"timestamp":3307507634768,"id":763,"parentId":720,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/hydration-diff/diff-view.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4423,"timestamp":3307507634717,"id":761,"parentId":733,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/hooks/use-on-click-outside.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":4668,"timestamp":3307507634740,"id":762,"parentId":733,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/hooks/use-measure-height.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":8966,"timestamp":3307507634807,"id":765,"parentId":722,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/thumbs/thumbs-up.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9196,"timestamp":3307507634864,"id":768,"parentId":745,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/copy-stack-trace-button.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9549,"timestamp":3307507634883,"id":769,"parentId":745,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/docs-link-button.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9861,"timestamp":3307507634827,"id":766,"parentId":722,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/thumbs/thumbs-down.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10048,"timestamp":3307507634903,"id":770,"parentId":710,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/assign-location.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10451,"timestamp":3307507634942,"id":772,"parentId":706,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10715,"timestamp":3307507634961,"id":773,"parentId":706,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":11244,"timestamp":3307507634923,"id":771,"parentId":711,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/fill-cache-with-new-subtree-data.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":12067,"timestamp":3307507634846,"id":767,"parentId":745,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/nodejs-inspector-button.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":12134,"timestamp":3307507635019,"id":776,"parentId":708,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":12509,"timestamp":3307507634982,"id":774,"parentId":706,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/handle-mutable.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":14148,"timestamp":3307507635057,"id":778,"parentId":705,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/should-hard-navigate.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":14566,"timestamp":3307507635038,"id":777,"parentId":705,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/invalidate-cache-below-flight-segmentpath.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-css","duration":1150610,"timestamp":3307506499058,"id":594,"parentId":572,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/src/app/globals.css.webpack[javascript/auto]!=!/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/src/app/globals.css","layer":null},"startTime":1750786510999,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":15041,"timestamp":3307507635076,"id":779,"parentId":705,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/clear-cache-node-data-for-segment-path.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":36528,"timestamp":3307507613678,"id":756,"parentId":637,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/react-server-dom-webpack/client.browser.js","layer":"app-pages-browser"},"startTime":1750786512113,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10281,"timestamp":3307507640331,"id":781,"parentId":710,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/shared/lib/server-reference-info.js","layer":"app-pages-browser"},"startTime":1750786512140,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":16155,"timestamp":3307507635095,"id":780,"parentId":705,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/aliased-prefetch-navigations.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":22378,"timestamp":3307507635001,"id":775,"parentId":707,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/ppr-navigations.js","layer":"app-pages-browser"},"startTime":1750786512135,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":28554,"timestamp":3307507629459,"id":757,"parentId":658,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/react/cjs/react-jsx-runtime.development.js","layer":"app-pages-browser"},"startTime":1750786512129,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":31121,"timestamp":3307507629500,"id":758,"parentId":671,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/react/cjs/react.development.js","layer":"app-pages-browser"},"startTime":1750786512129,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1870,"timestamp":3307507662493,"id":782,"parentId":763,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/collapse-icon.js","layer":"app-pages-browser"},"startTime":1750786512162,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":2145,"timestamp":3307507662567,"id":783,"parentId":764,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/body-locker.js","layer":"app-pages-browser"},"startTime":1750786512162,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1203,"timestamp":3307507664941,"id":784,"parentId":769,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/react-dev-overlay/ui/utils/parse-url-from-text.js","layer":"app-pages-browser"},"startTime":1750786512165,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6761,"timestamp":3307507665778,"id":785,"parentId":756,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js","layer":"app-pages-browser"},"startTime":1750786512166,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":6833,"timestamp":3307507666412,"id":787,"parentId":727,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/react-dom/cjs/react-dom.development.js","layer":"app-pages-browser"},"startTime":1750786512166,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":9207,"timestamp":3307507666498,"id":789,"parentId":771,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/client/components/router-reducer/invalidate-cache-by-router-state.js","layer":"app-pages-browser"},"startTime":1750786512166,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10487,"timestamp":3307507666429,"id":788,"parentId":754,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/react-refresh/cjs/react-refresh-runtime.development.js","layer":"app-pages-browser"},"startTime":1750786512166,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":10420,"timestamp":3307507666709,"id":790,"parentId":594,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js","layer":null},"startTime":1750786512167,"traceId":"027b520ae38c0c54"},{"name":"add-entry","duration":1291149,"timestamp":3307506385996,"id":510,"parentId":509,"tags":{"request":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/@next/react-refresh-utils/dist/runtime.js"},"startTime":1750786510886,"traceId":"027b520ae38c0c54"},{"name":"build-module-css","duration":1201658,"timestamp":3307506480400,"id":572,"parentId":515,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/src/app/globals.css","layer":"app-pages-browser"},"startTime":1750786510980,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":66324,"timestamp":3307507666392,"id":786,"parentId":728,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js","layer":"app-pages-browser"},"startTime":1750786512166,"traceId":"027b520ae38c0c54"},{"name":"build-module","duration":73,"timestamp":3307507734196,"id":791,"parentId":572,"tags":{},"startTime":1750786512234,"traceId":"027b520ae38c0c54"},{"name":"add-entry","duration":1348288,"timestamp":3307506386166,"id":512,"parentId":509,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750786510886,"traceId":"027b520ae38c0c54"},{"name":"build-module","duration":26,"timestamp":3307507734524,"id":792,"parentId":549,"tags":{"layer":null},"startTime":1750786512234,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":646,"timestamp":3307507736112,"id":793,"parentId":786,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/scheduler/index.js","layer":"app-pages-browser"},"startTime":1750786512236,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":17983,"timestamp":3307507737538,"id":794,"parentId":758,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/build/polyfills/process.js","layer":"app-pages-browser"},"startTime":1750786512237,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":125272,"timestamp":3307507737931,"id":795,"parentId":793,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/scheduler/cjs/scheduler.development.js","layer":"app-pages-browser"},"startTime":1750786512238,"traceId":"027b520ae38c0c54"},{"name":"build-module-js","duration":1522,"timestamp":3307507865783,"id":796,"parentId":794,"tags":{"name":"/Users/<USER>/Documents/Projects/my-projects/flymus-frontend/node_modules/next/dist/compiled/process/browser.js","layer":"app-pages-browser"},"startTime":1750786512366,"traceId":"027b520ae38c0c54"},{"name":"add-entry","duration":1481286,"timestamp":3307506386147,"id":511,"parentId":509,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750786510886,"traceId":"027b520ae38c0c54"},{"name":"add-entry","duration":1481167,"timestamp":3307506386272,"id":514,"parentId":509,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjebra%2FDocuments%2FProjects%2Fmy-projects%2Fflymus-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750786510886,"traceId":"027b520ae38c0c54"},{"name":"make","duration":1484415,"timestamp":3307506383090,"id":509,"parentId":508,"tags":{},"startTime":1750786510883,"traceId":"027b520ae38c0c54"},{"name":"chunk-graph","duration":2873,"timestamp":3307507871691,"id":798,"parentId":797,"tags":{},"startTime":1750786512372,"traceId":"027b520ae38c0c54"},{"name":"optimize-modules","duration":5,"timestamp":3307507874587,"id":800,"parentId":797,"tags":{},"startTime":1750786512374,"traceId":"027b520ae38c0c54"},{"name":"optimize-chunks","duration":57,"timestamp":3307507874640,"id":801,"parentId":797,"tags":{},"startTime":1750786512374,"traceId":"027b520ae38c0c54"},{"name":"optimize-tree","duration":7,"timestamp":3307507874712,"id":802,"parentId":797,"tags":{},"startTime":1750786512375,"traceId":"027b520ae38c0c54"},{"name":"optimize-chunk-modules","duration":4,"timestamp":3307507874756,"id":803,"parentId":797,"tags":{},"startTime":1750786512375,"traceId":"027b520ae38c0c54"},{"name":"optimize","duration":1165,"timestamp":3307507874579,"id":799,"parentId":797,"tags":{},"startTime":1750786512374,"traceId":"027b520ae38c0c54"},{"name":"module-hash","duration":4513,"timestamp":3307507877574,"id":804,"parentId":797,"tags":{},"startTime":1750786512377,"traceId":"027b520ae38c0c54"},{"name":"code-generation","duration":13983,"timestamp":3307507882105,"id":805,"parentId":797,"tags":{},"startTime":1750786512382,"traceId":"027b520ae38c0c54"},{"name":"hash","duration":9125,"timestamp":3307507897793,"id":806,"parentId":797,"tags":{},"startTime":1750786512398,"traceId":"027b520ae38c0c54"},{"name":"code-generation-jobs","duration":217,"timestamp":3307507906917,"id":807,"parentId":797,"tags":{},"startTime":1750786512407,"traceId":"027b520ae38c0c54"},{"name":"module-assets","duration":60,"timestamp":3307507907122,"id":808,"parentId":797,"tags":{},"startTime":1750786512407,"traceId":"027b520ae38c0c54"},{"name":"create-chunk-assets","duration":88283,"timestamp":3307507907185,"id":809,"parentId":797,"tags":{},"startTime":1750786512407,"traceId":"027b520ae38c0c54"},{"name":"NextJsBuildManifest-generateClientManifest","duration":345,"timestamp":3307507996758,"id":811,"parentId":508,"tags":{},"startTime":1750786512497,"traceId":"027b520ae38c0c54"},{"name":"NextJsBuildManifest-createassets","duration":690,"timestamp":3307507996419,"id":810,"parentId":508,"tags":{},"startTime":1750786512496,"traceId":"027b520ae38c0c54"},{"name":"seal","duration":128666,"timestamp":3307507870583,"id":797,"parentId":508,"tags":{},"startTime":1750786512370,"traceId":"027b520ae38c0c54"},{"name":"webpack-compilation","duration":1616670,"timestamp":3307506382635,"id":508,"parentId":285,"tags":{"name":"client"},"startTime":1750786510882,"traceId":"027b520ae38c0c54"},{"name":"emit","duration":38907,"timestamp":3307507999356,"id":812,"parentId":285,"tags":{},"startTime":1750786512499,"traceId":"027b520ae38c0c54"},{"name":"compile-path","duration":2399918,"timestamp":3307505640676,"id":118,"tags":{"trigger":"/"},"startTime":1750786510140,"traceId":"027b520ae38c0c54"},{"name":"webpack-invalidated-client","duration":2044118,"timestamp":3307505997462,"id":285,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750786510497,"traceId":"027b520ae38c0c54"}]
